#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IntentFix 启动脚本
提供命令行接口来运行项目
"""

import argparse
import json
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import IntentFixService
from config.settings import settings


def run_demo():
    """运行演示"""
    print("运行 IntentFix 演示...")
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), 'examples'))
    from demo import main as demo_main
    demo_main()


def run_tests():
    """运行测试"""
    print("运行 IntentFix 测试...")
    import unittest
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = loader.discover('tests', pattern='test_*.py')
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


def run_interactive():
    """运行交互式模式"""
    print("IntentFix 交互式模式")
    print("输入 'quit' 退出")
    print("=" * 50)
    
    service = IntentFixService()
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n请输入查询: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("再见！")
                break
            
            if not user_input:
                continue
            
            # 准备测试数据
            gptcontent = {"intent": "查询航班"}
            slot = {"intent": "查询航班"}
            userHistory = [
                {
                    "role": "user",
                    "time": datetime.now().isoformat(),
                    "type": "text",
                    "content": user_input
                }
            ]
            
            # 执行意图修正
            result = service.fix_intent(
                gptcontent=gptcontent,
                slot=slot,
                userHistory=userHistory,
                domain="flight"
            )
            
            # 显示结果
            print("\n结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
        except KeyboardInterrupt:
            print("\n\n再见！")
            break
        except Exception as e:
            print(f"错误: {e}")


def run_batch(input_file: str, output_file: str):
    """批量处理模式"""
    print(f"批量处理模式: {input_file} -> {output_file}")
    
    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            input_data = json.load(f)
        
        service = IntentFixService()
        results = []
        
        # 处理每个输入
        for i, item in enumerate(input_data):
            print(f"处理第 {i+1}/{len(input_data)} 个输入...")
            
            try:
                print("输入入参:", {
                    'gptcontent': item.get('gptcontent', {}),
                    'slot': item.get('slot', {}),
                    'userHistory': item.get('userHistory', []),
                    'domain': item.get('domain', 'flight')
                })
                result = service.fix_intent(
                    gptcontent=item.get('gptcontent', {}),
                    slot=item.get('slot', {}),
                    userHistory=item.get('userHistory', []),
                    domain=item.get('domain', 'flight')
                )
                print("输出出参:", result)
                
                results.append({
                    'input': item,
                    'output': result,
                    'success': True
                })
                
            except Exception as e:
                results.append({
                    'input': item,
                    'output': {'error': str(e)},
                    'success': False
                })
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"处理完成，结果保存到: {output_file}")
        
    except Exception as e:
        print(f"批量处理失败: {e}")


def show_config():
    """显示配置信息"""
    print("IntentFix 配置信息:")
    print("=" * 50)
    print(f"项目名称: {settings.PROJECT_NAME}")
    print(f"版本: {settings.VERSION}")
    print(f"调试模式: {settings.DEBUG}")
    print(f"置信度阈值: {settings.CONFIDENCE_THRESHOLD}")
    print(f"澄清阈值: {settings.CLARIFICATION_THRESHOLD}")
    print(f"支持的域: {list(settings.DOMAINS.keys())}")
    print(f"日志级别: {settings.LOG_LEVEL}")
    print(f"日志文件: {settings.LOG_FILE}")


def validate_config():
    """验证配置"""
    print("验证配置...")
    
    if settings.validate_config():
        print("✓ 配置验证通过")
        return True
    else:
        print("✗ 配置验证失败")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="IntentFix 意图修正模块",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run.py demo                    # 运行演示
  python run.py test                   # 运行测试
  python run.py interactive            # 交互式模式
  python run.py batch input.json output.json  # 批量处理
  python run.py config                 # 显示配置
  python run.py validate               # 验证配置
        """
    )
    
    parser.add_argument(
        'command',
        choices=['demo', 'test', 'interactive', 'batch', 'config', 'validate'],
        help='要执行的命令'
    )
    
    parser.add_argument(
        '--input', '-i',
        help='批量处理的输入文件'
    )
    
    parser.add_argument(
        '--output', '-o',
        help='批量处理的输出文件'
    )
    
    args = parser.parse_args()
    
    # 执行对应命令
    if args.command == 'demo':
        run_demo()
    elif args.command == 'test':
        success = run_tests()
        sys.exit(0 if success else 1)
    elif args.command == 'interactive':
        run_interactive()
    elif args.command == 'batch':
        if not args.input or not args.output:
            print("错误: 批量处理需要指定输入和输出文件")
            print("用法: python run.py batch input.json output.json")
            sys.exit(1)
        run_batch(args.input, args.output)
    elif args.command == 'config':
        show_config()
    elif args.command == 'validate':
        success = validate_config()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main() 