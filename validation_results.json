{"timestamp": "2025-08-20T11:44:17.324040", "results": [{"name": "tmall_flights_xian_changchun_default_today", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"arrival_time": "", "dst": "长春", "org": "西安", "questions": [], "cabin": "", "itinerary_num": 1, "sort": "departure_time_asc", "intent": "flights", "airport": "", "返回日期": "", "max_price": 0, "航班/车次号": "", "min_price": 0, "plane_type": "", "出发日期": "2025-08-20", "airline": "", "departure_time": "", "主业务类型": "机票"}, "org": "西安", "dst": "长春", "主业务类型": "机票", "出发日期": "2025-08-20", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_train_xiamen_fuzhou_20231005", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "train", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "福州", "org": "厦门", "questions": [], "出发日期": "2025-08-20", "itinerary_num": 1, "intent": "train", "主业务类型": "火车票", "airport": "", "departure_time": "", "airline": ""}, "org": "厦门", "dst": "福州", "主业务类型": "火车票", "出发日期": "2025-08-20", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_flights_no_time_defaults_today", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"org": "北京", "dst": "上海", "出发日期": "2025-08-20", "intent": "flights", "主业务类型": "机票", "airport": "", "questions": [], "departure_time": "", "airline": ""}, "org": "北京", "dst": "上海", "主业务类型": "机票", "出发日期": "2025-08-20", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_flights_query_tomorrow", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"org": "北京", "dst": "上海", "出发日期": "2025-08-21", "intent": "flights", "主业务类型": "机票", "airport": "", "questions": [], "departure_time": "", "airline": ""}, "org": "北京", "dst": "上海", "主业务类型": "机票", "出发日期": "2025-08-21", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_train_history_tomorrow", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "train", "parameters": {"org": "上海", "dst": "北京", "出发日期": "2025-08-21", "intent": "train", "主业务类型": "火车票", "airport": "", "questions": [], "departure_time": "", "airline": ""}, "org": "上海", "dst": "北京", "主业务类型": "火车票", "出发日期": "2025-08-21", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_train_to_flight_switch", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"arrival_time": "", "dst": "", "org": "", "questions": [], "cabin": "", "itinerary_num": "1", "sort": "departure_time_asc", "intent": "flights", "airport": "", "返回日期": "", "max_price": 0, "航班/车次号": "DZ6228", "min_price": 0, "plane_type": "", "出发日期": "2025-08-20", "airline": "DZ", "departure_time": "", "主业务类型": "机票"}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "2025-08-20", "航班/车次号": "DZ6228", "airport": "", "questions": [], "departure_time": "", "airline": "DZ"}}}, {"name": "tmall_jd5217_flight_number", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"arrival_time": "", "dst": "", "org": "", "questions": [], "cabin": "", "itinerary_num": "1", "sort": "departure_time_asc", "intent": "flights", "airport": "", "返回日期": "", "max_price": 0, "航班/车次号": "JD5217", "min_price": 0, "plane_type": "", "出发日期": "2025-08-20", "airline": "JD", "departure_time": "", "主业务类型": "机票"}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "2025-08-20", "航班/车次号": "JD5217", "airport": "", "questions": [], "departure_time": "", "airline": "JD"}}}, {"name": "tmall_cz8856_southern_airlines", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"arrival_time": "", "dst": "", "org": "", "questions": [], "cabin": "", "itinerary_num": "1", "sort": "departure_time_asc", "intent": "flights", "airport": "", "返回日期": "", "max_price": 0, "航班/车次号": "CZ8856", "min_price": 0, "plane_type": "", "出发日期": "2025-08-20", "airline": "CZ", "departure_time": "", "主业务类型": "机票"}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "2025-08-20", "航班/车次号": "CZ8856", "airport": "", "questions": [], "departure_time": "", "airline": "CZ"}}}, {"name": "tmall_flight_to_train_switch", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "train", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "济南", "org": "北京", "questions": [], "出发日期": "2025-08-22", "itinerary_num": "1", "intent": "train", "主业务类型": "火车票", "airport": "", "departure_time": "", "airline": ""}, "org": "北京", "dst": "济南", "主业务类型": "火车票", "出发日期": "2025-08-22", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_flight_price_followup", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "长春", "org": "无锡", "questions": [], "出发日期": "2025-08-22", "itinerary_num": "1", "sort": "departure_time_asc", "intent": "flights", "主业务类型": "机票", "airport": "", "departure_time": "", "airline": ""}, "org": "无锡", "dst": "长春", "主业务类型": "机票", "出发日期": "2025-08-22", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_train_d36_with_context_change_city", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "train", "parameters": {"返回日期": "", "航班/车次号": "D36", "dst": "乌鲁木齐", "org": "武汉", "questions": [], "出发日期": "2025-08-22", "itinerary_num": "1", "sort": "departure_time_asc", "intent": "train", "主业务类型": "火车票", "airport": "", "departure_time": "", "airline": ""}, "org": "武汉", "dst": "乌鲁木齐", "主业务类型": "火车票", "出发日期": "2025-08-22", "航班/车次号": "D36", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_flights_chengdu_kunming_cabin_price_followup", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"arrival_time": "", "dst": "昆明", "org": "成都", "questions": [], "cabin": "Y", "itinerary_num": "1", "sort": "departure_time_asc", "intent": "flights", "airport": "", "返回日期": "", "max_price": "0", "航班/车次号": "", "min_price": "0", "plane_type": "", "出发日期": "2025-08-21", "airline": "", "departure_time": "", "主业务类型": "机票"}, "org": "成都", "dst": "昆明", "主业务类型": "机票", "出发日期": "2025-08-21", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_beijing_to_nanning_price_should_be_train", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "train", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "南宁", "org": "北京", "questions": [], "出发日期": "2025-08-20", "itinerary_num": 1, "intent": "train", "主业务类型": "火车票", "airport": "", "departure_time": "", "airline": ""}, "org": "北京", "dst": "南宁", "主业务类型": "火车票", "出发日期": "2025-08-20", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_train_xinyi_to_lianyungang_20250820_earliest", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "train", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "连云港", "org": "新沂", "questions": [], "出发日期": "2025-08-20", "itinerary_num": "1", "intent": "train", "主业务类型": "火车票", "airport": "", "departure_time": "", "airline": ""}, "org": "新沂", "dst": "连云港", "主业务类型": "火车票", "出发日期": "2025-08-20", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_yibin_guangzhou_0911_date_extract", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "train", "parameters": {"dst": "广州", "org": "宜宾", "出发日期": "2025-09-11", "intent": "train", "主业务类型": "火车票", "airport": "", "questions": [], "departure_time": "", "airline": ""}, "org": "宜宾", "dst": "广州", "主业务类型": "火车票", "出发日期": "2025-09-11", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_beijing_doha_0820_0640_date_extract", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"dst": "多哈", "org": "北京", "出发日期": "2025-08-20", "intent": "flights", "主业务类型": "机票", "airport": "", "questions": [], "departure_time": "", "airline": ""}, "org": "北京", "dst": "多哈", "主业务类型": "机票", "出发日期": "2025-08-20", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_first_turn_no_city_no_number_defaults_normal", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "normal", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "", "org": "", "questions": ["军人购买高铁票有优惠吗？", "现役军人坐高铁有票价减免吗？", "军人证可以买高铁优惠票吗？", "部队人员乘坐高铁有优惠待遇吗？", "军人高铁票优惠政策是什么？"], "出发日期": "", "itinerary_num": 1, "主业务类型": "机票", "intent": "normal", "airport": "", "departure_time": "", "airline": ""}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "", "返回日期": "", "航班/车次号": "", "airport": "", "questions": ["军人购买高铁票有优惠吗？", "现役军人坐高铁有票价减免吗？", "军人证可以买高铁优惠票吗？", "部队人员乘坐高铁有优惠待遇吗？", "军人高铁票优惠政策是什么？"], "departure_time": "", "airline": ""}}}, {"name": "tmall_airport_terminal_info_query", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "normal", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "", "org": "", "questions": ["白云机场T2航站楼A区和B区有什么不同？", "广州白云机场T2航站楼A区与B区的功能区分是什么？", "在白云机场T2航站楼，A区和B区分别有哪些航空公司使用？", "白云机场T2的A区和B区在旅客服务设施上有何差异？", "从出行便利性角度看，白云机场T2航站楼A区和B区有什么区别？"], "出发日期": "", "itinerary_num": 1, "主业务类型": "机票", "intent": "normal", "airport": "白云机场", "departure_time": "", "airline": ""}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "", "返回日期": "", "航班/车次号": "", "airport": "白云机场", "questions": ["白云机场T2航站楼A区和B区有什么不同？", "广州白云机场T2航站楼A区与B区的功能区分是什么？", "在白云机场T2航站楼，A区和B区分别有哪些航空公司使用？", "白云机场T2的A区和B区在旅客服务设施上有何差异？", "从出行便利性角度看，白云机场T2航站楼A区和B区有什么区别？"], "departure_time": "", "airline": ""}}}, {"name": "tmall_flight_cz3850_plane_type_query", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"arrival_time": "", "dst": "", "org": "", "questions": ["CZ3850航班使用的是大型飞机吗？", "CZ3850航班执飞的机型是大机型还是小机型？", "南航CZ3850航班通常用什么型号的飞机执飞？", "CZ3850航班的飞机大小如何？", "CZ3850是宽体机还是窄体机？"], "cabin": "", "itinerary_num": 1, "sort": "departure_time_asc", "intent": "flights", "airport": "", "返回日期": "", "航班/车次号": "CZ3850", "max_price": 0, "min_price": 0, "plane_type": "", "出发日期": "2025-08-20", "airline": "CZ", "主业务类型": "机票", "departure_time": ""}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "2025-08-20", "航班/车次号": "CZ3850", "airport": "", "questions": ["CZ3850航班使用的是大型飞机吗？", "CZ3850航班执飞的机型是大机型还是小机型？", "南航CZ3850航班通常用什么型号的飞机执飞？", "CZ3850航班的飞机大小如何？", "CZ3850是宽体机还是窄体机？"], "departure_time": "", "airline": "CZ"}}}, {"name": "tmall_train_g1277_wifi_query", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "train", "parameters": {"arrival_time": "", "dst": "", "org": "", "questions": ["G1277次列车提供WiFi吗？", "乘坐G1277次高铁有无线网络可以使用吗？", "G1277次动车组是否配备上网服务？", "在G1277列车上能否连接Wi-Fi？", "G1277次车有没有提供车上互联网服务？"], "cabin": "", "itinerary_num": 1, "sort": "departure_time_asc", "intent": "train", "airport": "", "返回日期": "", "航班/车次号": "G1277", "max_price": 0, "min_price": 0, "plane_type": "", "出发日期": "2025-08-20", "airline": "", "主业务类型": "火车票", "departure_time": ""}, "org": "", "dst": "", "主业务类型": "火车票", "出发日期": "2025-08-20", "航班/车次号": "G1277", "airport": "", "questions": ["G1277次列车提供WiFi吗？", "乘坐G1277次高铁有无线网络可以使用吗？", "G1277次动车组是否配备上网服务？", "在G1277列车上能否连接Wi-Fi？", "G1277次车有没有提供车上互联网服务？"], "departure_time": "", "airline": ""}}}, {"name": "tmall_flight_mu9226_meal_query", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"返回日期": "", "航班/车次号": "MU9226", "dst": "", "org": "", "questions": ["MU9226航班提供什么餐食", "东航MU9226航班有餐食服务吗", "东方航空MU9226航班的餐食标准是什么", "MU9226航班经济舱餐食内容是什么", "乘坐MU9226航班可以自带食物吗"], "出发日期": "2025-08-20", "itinerary_num": 1, "主业务类型": "机票", "intent": "flights", "airport": "", "departure_time": "", "airline": "MU"}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "2025-08-20", "航班/车次号": "MU9226", "airport": "", "questions": ["MU9226航班提供什么餐食", "东航MU9226航班有餐食服务吗", "东方航空MU9226航班的餐食标准是什么", "MU9226航班经济舱餐食内容是什么", "乘坐MU9226航班可以自带食物吗"], "departure_time": "", "airline": "MU"}}}, {"name": "tmall_airport_transit_layover_service_query", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "normal", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "", "org": "", "questions": ["中转机场停留16小时有什么服务安排？", "长时间中转停留机场提供哪些便民服务？", "航空公司对长时间中转旅客有什么安排？", "机场中转区有休息或住宿服务吗？", "中转停留期间可以享受哪些免费服务？"], "出发日期": "", "itinerary_num": 1, "intent": "normal", "主业务类型": "机票", "airport": "", "departure_time": "", "airline": ""}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "", "返回日期": "", "航班/车次号": "", "airport": "", "questions": ["中转机场停留16小时有什么服务安排？", "长时间中转停留机场提供哪些便民服务？", "航空公司对长时间中转旅客有什么安排？", "机场中转区有休息或住宿服务吗？", "中转停留期间可以享受哪些免费服务？"], "departure_time": "", "airline": ""}}}, {"name": "tmall_senior_discount_high_speed_rail_question", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "normal", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "", "org": "", "questions": ["60岁以上老人购买高铁票有优惠吗？", "老年人乘坐高铁有票价优惠政策吗？", "高铁对老年乘客有特殊票价吗？", "60岁以上老人坐高铁能享受折扣吗？", "老年人高铁票价有减免政策吗？"], "出发日期": "", "itinerary_num": 1, "intent": "normal", "主业务类型": "火车票", "airport": "", "departure_time": "", "airline": ""}, "org": "", "dst": "", "主业务类型": "火车票", "出发日期": "", "返回日期": "", "航班/车次号": "", "airport": "", "questions": ["60岁以上老人购买高铁票有优惠吗？", "老年人乘坐高铁有票价优惠政策吗？", "高铁对老年乘客有特殊票价吗？", "60岁以上老人坐高铁能享受折扣吗？", "老年人高铁票价有减免政策吗？"], "departure_time": "", "airline": ""}}}, {"name": "tmall_jd5128_flight_query", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"主业务类型": "机票", "intent": "flights", "questions": [], "航班/车次号": "JD5128", "出发日期": "2025-08-20", "org": "", "dst": "", "返回日期": "", "airport": "", "itinerary_num": 1, "sort": "departure_time_asc", "departure_time": "", "airline": "JD"}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "2025-08-20", "航班/车次号": "JD5128", "airport": "", "questions": [], "departure_time": "", "airline": "JD"}}}, {"name": "tmall_flights_shanghai_morning_departure", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"arrival_time": "", "dst": "上海", "org": "上海", "questions": [], "cabin": "", "itinerary_num": 1, "sort": "departure_time_asc", "intent": "flights", "airport": "", "返回日期": "", "max_price": 0, "航班/车次号": "", "min_price": 0, "plane_type": "", "出发日期": "2025-08-22", "airline": "", "departure_time": "06:00-12:00", "主业务类型": "机票"}, "org": "上海", "dst": "上海", "主业务类型": "机票", "出发日期": "2025-08-22", "airport": "", "questions": [], "departure_time": "06:00-12:00", "airline": ""}}}, {"name": "tmall_train_gaotie_belongs_to_train_query", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "normal", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "", "org": "", "questions": ["高铁和火车有什么区别？", "高铁是否属于火车的一种？", "高速铁路与普通铁路的分类关系是什么？", "高铁在交通工具分类中属于哪一类？", "高铁和动车、普通火车的关系是什么？"], "出发日期": "", "itinerary_num": "1", "sort": "departure_time_asc", "intent": "normal", "主业务类型": "火车票", "airport": "", "departure_time": "", "airline": ""}, "org": "", "dst": "", "主业务类型": "火车票", "出发日期": "", "返回日期": "", "航班/车次号": "", "airport": "", "questions": ["高铁和火车有什么区别？", "高铁是否属于火车的一种？", "高速铁路与普通铁路的分类关系是什么？", "高铁在交通工具分类中属于哪一类？", "高铁和动车、普通火车的关系是什么？"], "departure_time": "", "airline": ""}}}, {"name": "tmall_flight_mu9226_meal_followup_query", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"arrival_time": "", "dst": "", "org": "", "questions": ["MU9226航班提供什么餐食", "MU9226航班的餐饮服务包含哪些内容", "乘坐MU9226航班有机上餐食吗", "东航MU9226航班的餐食标准是什么", "MU9226航班是否提供免费餐食"], "cabin": "", "itinerary_num": "1", "sort": "departure_time_asc", "intent": "flights", "airport": "", "返回日期": "", "航班/车次号": "MU9226", "max_price": 0, "min_price": 0, "plane_type": "", "出发日期": "2025-08-21", "airline": "MU", "主业务类型": "机票", "departure_time": ""}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "2025-08-21", "航班/车次号": "MU9226", "airport": "", "questions": ["MU9226航班提供什么餐食", "MU9226航班的餐饮服务包含哪些内容", "乘坐MU9226航班有机上餐食吗", "东航MU9226航班的餐食标准是什么", "MU9226航班是否提供免费餐食"], "departure_time": "", "airline": "MU"}}}, {"name": "tmall_flight_shanghai_beijing_morning_departure_followup", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "", "org": "上海", "questions": [], "出发日期": "2025-08-20", "itinerary_num": 1, "sort": "departure_time_asc", "intent": "flights", "主业务类型": "机票", "airport": "", "departure_time": "", "airline": ""}, "org": "上海", "dst": "", "主业务类型": "机票", "出发日期": "2025-08-20", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_flight_number_sequence_followup", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"返回日期": "", "航班/车次号": "MU9787", "dst": "", "org": "", "questions": [], "出发日期": "2025-08-22", "itinerary_num": 1, "sort": "departure_time_asc", "intent": "flights", "主业务类型": "机票", "airport": "", "departure_time": "", "airline": "MU"}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "2025-08-22", "航班/车次号": "MU9787", "airport": "", "questions": [], "departure_time": "", "airline": "MU"}}}, {"name": "tmall_airport_t2_transfer_baggage_checkin_query", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "normal", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "", "org": "", "questions": ["T2航站楼的中转区是否有托运行李的旅客办理登机手续的专用通道？", "在T2一层的中转区，托运行李旅客需要哪些手续才能登机？", "T2航站楼中转区是否为托运行李旅客提供标准化登机服务？", "在T2一层的中转区办理托运行李登机手续需要哪些证件？", "T2航站楼中转区是否支持托运行李旅客的快速登机手续办理？"], "出发日期": "", "itinerary_num": 1, "sort": "departure_time_asc", "intent": "normal", "主业务类型": "机票", "airport": "T2", "departure_time": "", "airline": ""}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "", "返回日期": "", "航班/车次号": "", "airport": "T2", "questions": ["T2航站楼的中转区是否有托运行李的旅客办理登机手续的专用通道？", "在T2一层的中转区，托运行李旅客需要哪些手续才能登机？", "T2航站楼中转区是否为托运行李旅客提供标准化登机服务？", "在T2一层的中转区办理托运行李登机手续需要哪些证件？", "T2航站楼中转区是否支持托运行李旅客的快速登机手续办理？"], "departure_time": "", "airline": ""}}}, {"name": "tmall_extract_past_date_auto_next_year", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"主业务类型": "机票", "intent": "flights", "questions": [], "航班/车次号": "", "出发日期": "2026-02-01", "org": "昆明", "dst": "杭州", "返回日期": "", "airport": "", "itinerary_num": 1, "sort": "departure_time_asc", "departure_time": "", "airline": ""}, "org": "昆明", "dst": "杭州", "主业务类型": "机票", "出发日期": "2026-02-01", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_flight_delay_inquiry_sequence", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"返回日期": "", "航班/车次号": "9C9980", "dst": "", "org": "", "questions": [], "出发日期": "2025-08-20", "itinerary_num": 1, "sort": "departure_time_asc", "airline": "9C", "intent": "flights", "主业务类型": "机票", "airport": "", "departure_time": ""}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "2025-08-20", "航班/车次号": "9C9980", "airport": "", "questions": [], "departure_time": "", "airline": "9C"}}}, {"name": "tmall_kuche_to_guangdong_train", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "train", "parameters": {"主业务类型": "火车票", "intent": "train", "questions": [], "航班/车次号": "", "出发日期": "2025-08-20", "org": "库车", "dst": "广州", "返回日期": "", "airport": "", "itinerary_num": 1, "sort": "departure_time_asc", "departure_time": "", "airline": ""}, "org": "库车", "dst": "广州", "主业务类型": "火车票", "出发日期": "2025-08-20", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_normal_ticket_change_fee_inquiry", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "normal", "parameters": {"主业务类型": "机票", "intent": "normal", "questions": ["高铁和火车有什么区别？", "高铁是否属于火车的一种？", "高速铁路与普通铁路的分类关系是什么？", "高铁在交通工具分类中属于哪一类？", "高铁和动车、普通火车的关系是什么？"], "航班/车次号": "", "出发日期": "", "org": "", "dst": "", "返回日期": "", "airport": "", "itinerary_num": 1, "airline": "", "departure_time": "", "arrival_time": "", "cabin": "", "min_price": 0, "max_price": 0, "plane_type": "", "sort": "departure_time_asc"}, "org": "", "dst": "", "主业务类型": "机票", "出发日期": "", "返回日期": "", "航班/车次号": "", "airport": "", "questions": ["高铁和火车有什么区别？", "高铁是否属于火车的一种？", "高速铁路与普通铁路的分类关系是什么？", "高铁在交通工具分类中属于哪一类？", "高铁和动车、普通火车的关系是什么？"], "departure_time": "", "airline": ""}}}, {"name": "tmall_flights_guangzhou_changsha_multi_turn", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"主业务类型": "机票", "intent": "flights", "questions": [], "航班/车次号": "", "出发日期": "2025-08-20", "org": "广州", "dst": "长沙", "返回日期": "", "airport": "", "itinerary_num": 1, "sort": "departure_time_asc", "departure_time": "", "airline": ""}, "org": "广州", "dst": "长沙", "主业务类型": "机票", "出发日期": "2025-08-20", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_flights_kunming_hangzhou_feb1_date_correction", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"返回日期": "", "航班/车次号": "", "dst": "杭州", "org": "昆明", "questions": [], "出发日期": "2026-02-01", "itinerary_num": 1, "intent": "flights", "主业务类型": "机票", "airport": "", "departure_time": "", "airline": ""}, "org": "昆明", "dst": "杭州", "主业务类型": "机票", "出发日期": "2026-02-01", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}, {"name": "tmall_flights_shanghai_departure_only", "pass": true, "errors": [], "output": {"gptcontent": {"intent": "flights", "parameters": {"arrival_time": "", "dst": "", "org": "上海", "questions": [], "cabin": "", "itinerary_num": 1, "sort": "departure_time_asc", "intent": "flights", "airport": "", "返回日期": "", "max_price": 0, "航班/车次号": "", "min_price": 0, "plane_type": "", "出发日期": "2025-08-20", "airline": "", "departure_time": "", "主业务类型": "机票"}, "org": "上海", "dst": "", "主业务类型": "机票", "出发日期": "2025-08-20", "airport": "", "questions": [], "departure_time": "", "airline": ""}}}]}