# IntentFix 意图修正模块

## 项目简介

IntentFix 是一个智能意图修正模块，用于在多轮对话过程中结合对话历史和初步识别结果，对用户意图进行校正和确认。该模块支持多业务域扩展，可根据不同domain自定义修复逻辑。

## 主要功能

### 1. 意图修正
- **规则驱动校验**：基于关键词和正则表达式的规则匹配
- **上下文修正**：结合历史对话进行意图继承和修正
- **多轮澄清确认**：当意图不明确时主动询问用户
- **置信度评估**：综合多个来源的置信度进行决策

### 2. 参数校验修正
- **参数规范性校验**：日期、航班号、城市名称等格式校验
- **参数一致性校验**：检测并解决历史参数冲突
- **动态更新机制**：实时同步用户主动修改的参数

### 3. 多域支持
- **航班域 (flight)**：支持航班查询、预订、延误等业务
- **列车域 (train)**：支持列车查询、预订、延误等业务
- **可扩展架构**：支持自定义新业务域

## 项目结构

```
IntentFix/
├── core/                    # 核心模块
│   ├── models.py           # 数据模型定义
│   ├── rule_engine.py      # 规则引擎
│   ├── rasa_classifier.py  # Rasa分类器
│   ├── parameter_validator.py # 参数校验器
│   └── intent_fix.py       # 意图修正主流程
├── domains/                # 业务域实现
│   ├── flight_domain.py    # 航班域
│   └── train_domain.py     # 列车域
├── tests/                  # 测试文件
│   └── test_intent_fix.py  # 单元测试和集成测试
├── main.py                 # 主入口文件
├── requirements.txt        # 依赖包
└── README.md              # 项目说明
```

## 安装和运行

### 1. 环境要求
- Python 3.8+
- pip

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 启动Web服务
```bash
# 开发模式（自动重载）
python server.py --reload

# 生产模式
python server.py --workers 4

# 指定端口
python server.py --port 8080

# 使用启动脚本
./start_server.sh
```

### 4. 访问API
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **服务状态**: http://localhost:8000/

### 5. 测试API
```bash
# 运行客户端测试
python client/test_api.py

# 使用curl测试
curl http://localhost:8000/health
```

### 6. 运行示例
```bash
python main.py
```

### 7. 运行测试
```bash
python -m unittest tests/test_intent_fix.py -v
```

## 使用说明

### Web API 使用

#### 1. 启动服务
```bash
python server.py --reload
```

#### 2. 调用API
```bash
# 意图修正API
curl -X POST http://localhost:8000/api/v1/fix-intent \
  -H "Content-Type: application/json" \
  -d '{
    "gptcontent": {
      "intent": "查询航班",
      "flightNo": "CA1234"
    },
    "slot": {
      "intent": "查询航班",
      "flightNo": "CA1234",
      "departCity": "北京",
      "arriveCity": "上海"
    },
    "userHistory": [
      {
        "role": "user",
        "time": "2025-01-15T10:30:00",
        "type": "text",
        "content": "CA1234航班什么时候起飞？"
      }
    ],
    "domain": "flight"
  }'
```

#### 3. Python客户端示例
```python
import requests

# 意图修正
response = requests.post(
    "http://localhost:8000/api/v1/fix-intent",
    json={
        "gptcontent": {"intent": "查询航班", "flightNo": "CA1234"},
        "slot": {"intent": "查询航班", "flightNo": "CA1234"},
        "userHistory": [{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "CA1234航班什么时候起飞？"}],
        "domain": "flight"
    }
)

result = response.json()
print(f"最终意图: {result['data']['gptcontent']['intent']}")
```

### 程序库使用

```python
from main import IntentFixService

# 创建服务实例
service = IntentFixService()

# 准备输入数据
gptcontent = {
    "intent": "查询航班",
    "flightNo": "CA1234"
}

slot = {
    "intent": "查询航班",
    "flightNo": "CA1234",
    "departCity": "北京",
    "arriveCity": "上海"
}

userHistory = [
    {
        "role": "user",
        "time": "2025-01-15T10:30:00",
        "type": "text",
        "content": "CA1234航班什么时候起飞？"
    }
]

# 执行意图修正
result = service.fix_intent(
    gptcontent=gptcontent,
    slot=slot,
    userHistory=userHistory,
    domain="flight"
)

print(result)
```

### 输入参数说明

- **gptcontent**: 大模型输出的初步意图和槽位参数（JSON）
- **slot**: Tripnow提取的槽位和意图信息（JSON）
- **userHistory**: 用户与系统的历史对话（数组）
- **domain**: 业务域（如 "flight", "train"）


### 输出格式

```json
{
  "gptcontent": {
    "intent": "延误登机",
    "flightNo": "CA1234",
    "departDate": "2025-01-15",
    "departCity": "北京",
    "arriveCity": "上海"
  },
  "resultReply": "请问您查询的是哪个航班号的延误信息？"
}
```

## API接口

### 1. 意图修正API
- **URL**: `POST /api/v1/fix-intent`
- **描述**: 执行意图修正
- **请求体**:
  ```json
  {
    "gptcontent": {
      "intent": "查询航班",
      "flightNo": "CA1234"
    },
    "slot": {
      "intent": "查询航班",
      "flightNo": "CA1234",
      "departCity": "北京",
      "arriveCity": "上海"
    },
    "userHistory": [
      {
        "role": "user",
        "time": "2025-01-15T10:30:00",
        "type": "text",
        "content": "CA1234航班什么时候起飞？"
      }
    ],
    "domain": "flight"
  }
  ```

### 2. 批量意图修正API
- **URL**: `POST /api/v1/batch-fix`
- **描述**: 批量执行意图修正
- **请求体**: 意图修正请求数组

### 3. 获取配置API
- **URL**: `GET /api/v1/config`
- **描述**: 获取服务配置信息

### 4. 获取支持的域API
- **URL**: `GET /api/v1/domains`
- **描述**: 获取支持的业务域列表

### 5. 健康检查API
- **URL**: `GET /health`
- **描述**: 检查服务健康状态

## 核心流程

### 1. 意图修正流程
1. **提取当前用户请求**：从历史对话中获取最新用户输入
2. **规则驱动校验**：应用关键词和模式匹配规则
3. **结果比对与初步判定**：比较规则结果与模型结果
4. **意图分类预测**：使用Rasa模型进行意图分类
5. **多轮澄清确认**：当意图不明确时进行用户确认
6. **参数校验修正**：校验和修正槽位参数
7. **输出最终结果**：返回修正后的意图和参数

### 2. 参数校验流程
1. **参数规范性校验**：格式化和标准化参数
2. **参数一致性校验**：检测历史参数冲突
3. **冲突解决**：明确修改 vs 隐含修改的处理
4. **动态更新**：实时同步用户修改的参数

## 扩展新业务域

### 1. 创建域引擎
```python
from core.intent_fix import IntentFixEngine

class CustomDomainEngine(IntentFixEngine):
    def __init__(self):
        super().__init__(domain="custom")
        self._setup_custom_rules()
    
    def _setup_custom_rules(self):
        # 添加自定义规则
        custom_keyword_rules = {
            "自定义意图": ["关键词1", "关键词2"]
        }
        self.add_custom_rules(
            domain="custom",
            keyword_rules=custom_keyword_rules
        )
```

### 2. 注册新域
```python
from main import IntentFixService

service = IntentFixService()
service.add_custom_domain("custom", CustomDomainEngine())
```

## 配置说明

### 置信度阈值
```python
engine = IntentFixEngine()
engine.set_confidence_thresholds(
    confidence_threshold=0.7,    # 高置信度阈值
    clarification_threshold=0.5   # 澄清阈值
)
```

### 自定义规则
```python
# 添加关键词规则
engine.rule_engine.add_keyword_rule("新意图", ["关键词1", "关键词2"])

# 添加模式规则
engine.rule_engine.add_pattern_rule("新意图", [r".*模式.*"])
```

## 测试用例

项目包含完整的测试套件：

- **单元测试**：测试各个组件的独立功能
- **集成测试**：测试完整的工作流程
- **边界测试**：测试异常情况和边界条件

运行测试：
```bash
python -m unittest discover tests -v
```

## 性能优化

### 1. 缓存机制
- 规则匹配结果缓存
- 参数校验结果缓存
- 意图分类结果缓存

### 2. 并发处理
- 支持多线程处理多个请求
- 异步处理长时间运行的任务

### 3. 内存优化
- 及时释放不需要的对象
- 使用生成器处理大量数据

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ModuleNotFoundError: No module named 'core'
   ```
   解决：确保在项目根目录运行

2. **依赖包错误**
   ```
   ImportError: No module named 'pydantic'
   ```
   解决：运行 `pip install -r requirements.txt`

3. **Rasa模型错误**
   ```
   Rasa model not found
   ```
   解决：检查Rasa项目配置，或使用模拟模式

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 参与讨论

---

**注意**：本项目仍在开发中，API 可能会发生变化。建议在生产环境使用前进行充分测试。 