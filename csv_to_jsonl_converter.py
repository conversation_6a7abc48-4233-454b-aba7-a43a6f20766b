#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV转JSONL转换器
将CSV格式的数据转换为训练用的JSONL格式
"""

import csv
import json
from collections import defaultdict
import ast
import re

def parse_extract_result(extract_result_str):
    """解析extract_result字符串为字典"""
    try:
        # 清理字符串，移除换行符和多余空格
        cleaned_str = re.sub(r'\s+', ' ', extract_result_str.strip())
        
        # 移除可能的引号并解析
        if cleaned_str.startswith("'") and cleaned_str.endswith("'"):
            cleaned_str = cleaned_str[1:-1]
        elif cleaned_str.startswith('"') and cleaned_str.endswith('"'):
            cleaned_str = cleaned_str[1:-1]
            
        return ast.literal_eval(cleaned_str)
    except Exception as e:
        print(f"解析extract_result失败: {e}")
        print(f"原始字符串: {extract_result_str}")
        # 如果解析失败，返回空字典
        return {}

def convert_csv_to_jsonl(csv_file_path, output_file_path, system_prompt_template):
    """
    将CSV文件转换为JSONL格式
    
    Args:
        csv_file_path: 输入的CSV文件路径
        output_file_path: 输出的JSONL文件路径
        system_prompt_template: 系统提示词模板
    """
    
    # 按sessionId分组数据
    session_data = defaultdict(list)
    
    # 手动读取CSV文件，处理多行数据
    with open(csv_file_path, 'r', encoding='utf-8-sig') as csvfile:  # 使用utf-8-sig处理BOM
        # 读取第一行作为列名
        header_line = csvfile.readline().strip()
        header = header_line.split(',')
        # 清理BOM字符
        header = [col.strip().replace('\ufeff', '') for col in header]
        print(f"列名: {header}")
        
        # 读取所有数据行
        content = csvfile.read()
        
        # 使用正则表达式分割行，考虑引号内的逗号
        lines = re.split(r'\n(?=(?:[^"]*"[^"]*")*[^"]*$)', content)
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 使用CSV解析器处理单行
            try:
                # 创建临时字符串IO对象
                from io import StringIO
                temp_file = StringIO(line)
                reader = csv.DictReader(temp_file, fieldnames=header)
                
                for row in reader:
                    session_id = row.get('sessionId', '').strip()
                    question = row.get('question', '').strip()
                    now_time = row.get('now_time', '').strip()
                    extract_result = row.get('extract_result', '').strip()
                    
                    if not session_id or not question:
                        continue
                    
                    # 解析extract_result
                    parsed_result = parse_extract_result(extract_result)
                    
                    session_data[session_id].append({
                        'question': question,
                        'now_time': now_time,
                        'extract_result': parsed_result
                    })
                    
            except Exception as e:
                print(f"处理行时出错: {e}")
                print(f"问题行: {line}")
                continue
    
    print(f"找到 {len(session_data)} 个会话")
    
    # 生成JSONL文件
    with open(output_file_path, 'w', encoding='utf-8') as jsonlfile:
        for session_id, messages in session_data.items():
            # 构建对话消息
            conversation_messages = []
            
            # 添加系统消息
            # 使用第一个消息的now_time作为参考日期
            reference_date = messages[0]['now_time'] if messages else ""
            system_content = system_prompt_template.format(now_time=reference_date)
            conversation_messages.append({
                "role": "system",
                "content": system_content
            })
            
            # 添加用户和助手消息
            for msg in messages:
                # 用户消息
                conversation_messages.append({
                    "role": "user",
                    "content": msg['question']
                })
                
                # 助手消息（使用extract_result作为回复）
                assistant_content = json.dumps(msg['extract_result'], ensure_ascii=False)
                conversation_messages.append({
                    "role": "assistant",
                    "content": assistant_content
                })
            
            # 写入JSONL文件
            jsonl_entry = {
                "messages": conversation_messages
            }
            jsonlfile.write(json.dumps(jsonl_entry, ensure_ascii=False) + '\n')
    
    return len(session_data)

def main():
    """主函数"""
    # 配置参数
    csv_file_path = "Tripnow_参数提取结果质检正确20250807.csv"
    output_file_path = "training_data.jsonl"
    
    # 系统提示词模板（你可以根据需要修改）
    system_prompt_template = """#角色你是高铁管家的AI助手，能解决乘坐高铁、火车、飞机时遇到的问题。

# 任务要求
## 回应准则
- 参考用户聊天的上下文，结合知识库查询结果回答问题。
- 用简洁语句回答，字数在80字以内。
- 禁止输出"航旅纵横""飞常准""飞猪""携程""智行""**""去哪儿"，禁止输出表情等特殊字符。
- 当用户没明确是高铁还是飞机时，根据具体情况判断来回复。
- 对用户的问题进行细节询问。

## 对话风格
以专业、口语化的拟人口吻进行回应，内容力求精简。

## 问题处理
分析用户问题，如果用户问题比较复杂，则进行多轮对话来回答用户的问题，不要直接使用知识库数据进行笼统回答。例如：对于像询问12306火车票改签规则的问题，要询问用户是开车前多久进行改签。

# 工作流程
- 当用户发送"你好"等问候语时，告知用户你是你好，我是出行知识AI助手，能帮你解决乘坐高铁、火车、飞机时遇到的问题。
- 其余情况根据用户输入解析需求并回答问题

参考当前日期：{now_time}"""
    
    try:
        session_count = convert_csv_to_jsonl(csv_file_path, output_file_path, system_prompt_template)
        print(f"转换完成！输出文件：{output_file_path}")
        print(f"处理了 {session_count} 个会话")
    except Exception as e:
        print(f"转换过程中出现错误：{e}")

if __name__ == "__main__":
    main() 