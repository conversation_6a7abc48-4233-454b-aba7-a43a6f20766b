#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试tmall domain输出格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.models import GptContent, IntentFixInput, IntentFixOutput, SlotInfo, UserMessage
from core.intent_fix import IntentFixEngine
from datetime import datetime


def test_tmall_output_format():
    """测试tmall domain输出格式"""
    
    # 初始化意图修正引擎
    engine = IntentFixEngine()
    
    # 测试用例：使用tmall domain的实际数据格式
    test_case = {
        "gptcontent": {
            "intent": "flights",
            "返回日期": "",
            "航班车次号": "CA1234",
            "dst": "北京",
            "org": "",
            "questions": [],
            "出发日期": "2025-07-30",
            "itinerary_num": "",
            "主业务类型": "机票",
            "airport": ""
        },
        "slot": {
            "intent": "flights",
            "出发日期": "2025-07-30",
            "返回日期": "",
            "主业务类型": "机票",
            "航班车次号": "CA1234",
            "org": "",
            "dst": "北京"
        },
        "userHistory": [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "CA1234航班什么时候起飞？"
            }
        ],
        "domain": "tmall"
    }
    
    print("=" * 60)
    print("tmall domain输出格式测试")
    print("=" * 60)
    
    # 构建输入数据
    input_data = IntentFixInput(
        gptcontent=GptContent(**test_case["gptcontent"]),
        slot=SlotInfo(**test_case["slot"]),
        userHistory=[UserMessage(**msg) for msg in test_case["userHistory"]],
        domain=test_case["domain"]
    )
    
    try:
        # 执行意图修正
        result = engine.fix_intent(input_data)
        
        print(f"\n输出结果:")
        print(f"意图: {result.gptcontent.intent}")
        
        # 检查tmall domain应该包含的字段
        tmall_fields = [
            "返回日期", "航班车次号", "dst", "org", "questions", 
            "出发日期", "itinerary_num", "主业务类型", "airport"
        ]
        
        print(f"\n应该包含的字段:")
        for field in tmall_fields:
            value = getattr(result.gptcontent, field, None)
            print(f"  {field}: {value}")
        
        # 检查不应该包含的字段
        unwanted_fields = ["departDate", "returnDate", "flightTrainNo", "mainBusinessType"]
        
        print(f"\n不应该包含的字段:")
        for field in unwanted_fields:
            value = getattr(result.gptcontent, field, None)
            if value is not None and value != "":
                print(f"  ✗ {field}: {value} (不应该存在)")
            else:
                print(f"  ✓ {field}: {value} (正确)")
        
        # 验证输出格式（排除None值）
        output_dict = result.gptcontent.model_dump(exclude_none=True)
        print(f"\n完整输出（排除None值）:")
        print(f"{output_dict}")
        
        # 检查是否有多余的字段
        extra_fields = []
        for key, value in output_dict.items():
            if key not in tmall_fields and key != "intent":
                extra_fields.append(key)
        
        if extra_fields:
            print(f"\n发现多余字段: {extra_fields}")
        else:
            print(f"\n✓ 输出格式正确，没有多余字段")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_other_domain_format():
    """测试其他domain的输出格式"""
    
    engine = IntentFixEngine()
    
    # 测试用例：使用其他domain格式
    test_case = {
        "gptcontent": {
            "intent": "flights",
            "departDate": "2025-01-15",
            "returnDate": "",
            "flightTrainNo": "MU5678",
            "dst": "上海",
            "org": "北京",
            "mainBusinessType": "机票"
        },
        "slot": {
            "intent": "flights",
            "departDate": "2025-01-15",
            "mainBusinessType": "机票"
        },
        "userHistory": [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "MU5678航班从北京到上海"
            }
        ],
        "domain": "flight"
    }
    
    print("\n" + "=" * 60)
    print("其他domain输出格式测试")
    print("=" * 60)
    
    input_data = IntentFixInput(
        gptcontent=GptContent(**test_case["gptcontent"]),
        slot=SlotInfo(**test_case["slot"]),
        userHistory=[UserMessage(**msg) for msg in test_case["userHistory"]],
        domain=test_case["domain"]
    )
    
    try:
        result = engine.fix_intent(input_data)
        
        print(f"\n输出结果:")
        print(f"意图: {result.gptcontent.intent}")
        
        # 检查其他domain应该包含的字段
        other_fields = [
            "departDate", "returnDate", "flightTrainNo", "dst", "org", "mainBusinessType"
        ]
        
        print(f"\n应该包含的字段:")
        for field in other_fields:
            value = getattr(result.gptcontent, field, None)
            print(f"  {field}: {value}")
        
        # 验证输出格式（排除None值）
        output_dict = result.gptcontent.model_dump(exclude_none=True)
        print(f"\n完整输出（排除None值）:")
        print(f"{output_dict}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_tmall_output_format()
    test_other_domain_format()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60) 