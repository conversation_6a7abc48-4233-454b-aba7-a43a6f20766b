#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
意图修正模块主入口
提供完整的意图修正服务接口
"""

import json
from copy import deepcopy
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from core.models import IntentFixInput, IntentFixOutput, GptContent, SlotInfo, UserMessage
from core.intent_fix import IntentFixEngine
from domains.flight_domain import FlightDomainEngine
from domains.train_domain import TrainDomainEngine


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class IntentFixService:
    """意图修正服务"""
    
    def __init__(self):
        self.engines = {
            "flight": FlightDomainEngine(),
            "train": TrainDomainEngine(),
            "default": IntentFixEngine()
        }
    
    def fix_intent(self, 
                   gptcontent: Dict[str, Any],
                   slot: Dict[str, Any],
                   userHistory: list,
                   domain: str = "flight") -> Dict[str, Any]:
        """
        意图修正主接口
        
        Args:
            gptcontent: 大模型输出的初步意图和槽位参数
            slot: Tripnow提取的槽位和意图信息
            userHistory: 用户与系统的历史对话
            domain: 业务域
            
        Returns:
            修正后的结果字典
        """
        try:
            # 入参日志
            try:
                logger.info("Service.fix_intent 入参: %s", {
                    "domain": domain,
                    "gptcontent": gptcontent,
                    "slot": slot,
                    "userHistory_size": len(userHistory) if isinstance(userHistory, list) else 'N/A'
                })
            except Exception:
                pass

            # 1. 数据验证和转换
            input_data = self._prepare_input_data(
                gptcontent, slot, userHistory, domain
            )
            
            # 2. 选择对应的引擎
            engine = self.engines.get(domain, self.engines["default"])
            
            # 3. 执行意图修正
            result = engine.fix_intent(input_data)
            
            # 4. 转换输出格式
            output = self._prepare_output_data(result, domain=domain, original_gptcontent=gptcontent)
            
            logger.info(f"意图修正完成 - 域: {domain}, 最终意图: {result.gptcontent.intent}")
            try:
                logger.info("Service.fix_intent 出参: %s", output)
            except Exception:
                pass
            
            return output
            
        except Exception as e:
            logger.error(f"意图修正失败: {str(e)}")
            return self._create_error_response(str(e))
    
    def _prepare_input_data(self, 
                           gptcontent: Dict[str, Any],
                           slot: Dict[str, Any],
                           userHistory: list,
                           domain: str) -> IntentFixInput:
        """准备输入数据"""
        # 规范化 gptcontent：合并 parameters，统一别名 "航班/车次号" -> "航班车次号"
        normalized_gpt: Dict[str, Any] = {}
        if isinstance(gptcontent, dict):
            normalized_gpt.update(gptcontent)
            params = gptcontent.get("parameters")
            if isinstance(params, dict):
                # 顶层优先，parameters 作为补充
                for k, v in params.items():
                    if k not in normalized_gpt:
                        normalized_gpt[k] = v
            # 字段别名统一
            if "航班/车次号" in normalized_gpt and "航班车次号" not in normalized_gpt:
                normalized_gpt["航班车次号"] = normalized_gpt.pop("航班/车次号")
            # 类型纠正：itinerary_num 可能为数字，统一转字符串
            if "itinerary_num" in normalized_gpt and not isinstance(normalized_gpt["itinerary_num"], str):
                try:
                    normalized_gpt["itinerary_num"] = str(normalized_gpt["itinerary_num"]) if normalized_gpt["itinerary_num"] is not None else None
                except Exception:
                    normalized_gpt["itinerary_num"] = None
            # 移除原始 parameters 容器，避免污染
            if "parameters" in normalized_gpt:
                normalized_gpt.pop("parameters", None)

        gpt_content = GptContent(
            intent=normalized_gpt.get("intent", "通用agent"),
            mainBusinessType=normalized_gpt.get("主业务类型"),
            **{k: v for k, v in normalized_gpt.items() if k not in ["intent", "主业务类型"]}
        )

        # 规范化 slot：合并 slots，统一别名
        normalized_slot: Dict[str, Any] = {}
        if isinstance(slot, dict):
            normalized_slot.update(slot)
            inner = slot.get("slots")
            if isinstance(inner, dict):
                for k, v in inner.items():
                    if k not in normalized_slot:
                        normalized_slot[k] = v
            if "航班/车次号" in normalized_slot and "航班车次号" not in normalized_slot:
                normalized_slot["航班车次号"] = normalized_slot.pop("航班/车次号")
            # 不需要保留 slots 容器
            if "slots" in normalized_slot:
                normalized_slot.pop("slots", None)

        slot_info = SlotInfo(
            intent=normalized_slot.get("intent"),
            departDate=normalized_slot.get("出发日期"),
            mainBusinessType=normalized_slot.get("主业务类型"),
            **{k: v for k, v in normalized_slot.items() if k not in ["intent", "departDate", "主业务类型"]}
        )
        
        # 转换userHistory
        user_messages = []
        for msg in userHistory:
            try:
                # 处理时间格式
                time_str = msg.get("time", "")
                if isinstance(time_str, str):
                    try:
                        time_obj = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
                    except:
                        time_obj = datetime.now()
                else:
                    time_obj = datetime.now()
                
                user_message = UserMessage(
                    role=msg.get("role", "user"),
                    time=time_obj,
                    type=msg.get("type", "text"),
                    content=msg.get("content", "")
                )
                user_messages.append(user_message)
            except Exception as e:
                logger.warning(f"跳过无效的历史消息: {e}")
                continue
        
        return IntentFixInput(
            gptcontent=gpt_content,
            slot=slot_info,
            userHistory=user_messages,
            domain=domain
        )
    
    def _prepare_output_data(self, result: IntentFixOutput, domain: Optional[str] = None, original_gptcontent: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """准备输出数据"""
        output = {"gptcontent": {}}
        
        # 判断是否 tmall 域（依据中文核心字段）
        is_tmall_domain = hasattr(result.gptcontent, '返回日期') or hasattr(result.gptcontent, '出发日期')
        
        if is_tmall_domain:
            # tmall 域：以原始 gptcontent 透传为基础，仅覆盖指定字段
            base = deepcopy(original_gptcontent) if isinstance(original_gptcontent, dict) else {}
            if not isinstance(base, dict):
                base = {}
            # intent 覆盖（顶层与 parameters 内若存在）
            base["intent"] = result.gptcontent.intent
            if isinstance(base.get("parameters"), dict):
                base["parameters"]["intent"] = result.gptcontent.intent

            def set_field(name: str, value: Any):
                # 对于normal意图，允许设置空字符串
                if value is None and result.gptcontent.intent != "normal":
                    return
                base[name] = value if value is not None else ""
                if isinstance(base.get("parameters"), dict):
                    base["parameters"][name] = value if value is not None else ""

            g = result.gptcontent
            # 覆盖 org / dst
            set_field("org", getattr(g, "org", None))
            set_field("dst", getattr(g, "dst", None))
            # 覆盖 主业务类型（机票/火车票）
            business_type = getattr(g, "主业务类型", None) or getattr(g, "mainBusinessType", None)
            # 对于normal意图，如果主业务类型为空，尝试从原始数据中获取
            if result.gptcontent.intent == "normal" and not business_type:
                business_type = original_gptcontent.get("parameters", {}).get("主业务类型", "") if original_gptcontent else ""
            set_field("主业务类型", business_type)
            # 覆盖 出发日期 / 返回日期（优先中文字段）
            depart_cn = getattr(g, "出发日期", None) or getattr(g, "departDate", None)
            return_cn = getattr(g, "返回日期", None) or getattr(g, "returnDate", None)
            set_field("出发日期", depart_cn)
            set_field("返回日期", return_cn)
            # 覆盖 航班/车次号（由内部字段映射）
            flight_train = getattr(g, "航班车次号", None) or getattr(g, "flightTrainNo", None)

            set_field("航班/车次号", flight_train)
            
            # 处理airport和questions字段
            if hasattr(g, "airport"):
                set_field("airport", getattr(g, "airport", None))
            if hasattr(g, "questions"):
                set_field("questions", getattr(g, "questions", None))
            
            # 处理departure_time字段
            if hasattr(g, "departure_time"):
                set_field("departure_time", getattr(g, "departure_time", None))
            
            # 处理airline字段
            if hasattr(g, "airline"):
                set_field("airline", getattr(g, "airline", None))

            output["gptcontent"] = base
        else:
            # 其他domain格式
            # 添加主业务类型
            output["gptcontent"]["intent"] = result.gptcontent.intent
            if result.gptcontent.mainBusinessType:
                output["gptcontent"]["mainBusinessType"] = result.gptcontent.mainBusinessType
            
            # 添加槽位信息 - 使用GptContent模型中实际存在的字段
            slot_fields = ["departDate", "mainBusinessType", "returnDate", "flightTrainNo", "出发日期", "返回日期", "主业务类型", "航班车次号", "org", "dst"]
            for field in slot_fields:
                value = getattr(result.gptcontent, field, None)
                if value:
                    output["gptcontent"][field] = value
            
            # 添加其他字段
            for key, value in result.gptcontent.model_dump(exclude_none=True).items():
                if key not in output["gptcontent"] and key not in ["intent", "mainBusinessType"]:
                    output["gptcontent"][key] = value
        
        # 添加澄清消息
        if result.resultReply:
            output["resultReply"] = result.resultReply
        
        return output
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "gptcontent": {
                "intent": "通用agent"
            },
            "error": error_message
        }
    
    def add_custom_domain(self, domain: str, engine: IntentFixEngine):
        """添加自定义域"""
        self.engines[domain] = engine
        logger.info(f"添加自定义域: {domain}")
    
    def get_supported_domains(self) -> list:
        """获取支持的域列表"""
        return list(self.engines.keys())


def main():
    """主函数 - 示例用法"""
    
    # 创建服务实例
    service = IntentFixService()
    
    # 示例输入数据
    example_input = {
        "gptcontent": {
            "intent": "flights",
            "返回日期": "",
            "航班车次号": "CA1234",
            "dst": "北京",
            "org": "",
            "questions": [],
            "出发日期": "2025-07-30",
            "itinerary_num": "",
            "主业务类型": "机票",
            "airport": ""
        },
        "slot": {
            "intent": "flights",
            "出发日期": "2025-07-30",
            "返回日期": "",
            "主业务类型": "机票",
            "航班车次号": "CA1234",
            "org": "",
            "dst": "北京"
        },
        "userHistory": [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "CA1234航班什么时候起飞？"
            },
            {
                "role": "system",
                "time": "2025-01-15T10:30:05",
                "type": "text",
                "content": "正在查询CA1234航班信息..."
            }
        ],
        "domain": "tmall"
    }
    
    # 执行意图修正
    result = service.fix_intent(
        gptcontent=example_input["gptcontent"],
        slot=example_input["slot"],
        userHistory=example_input["userHistory"],
        domain=example_input["domain"]
    )
    
    # 输出结果
    print("=== 意图修正结果 ===")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 测试列车域
    train_example = {
        "gptcontent": {
            "intent": "查询列车",
            "trainNo": "G1234"
        },
        "slot": {
            "intent": "查询列车",
            "trainNo": "G1234",
            "departCity": "北京",
            "arriveCity": "上海"
        },
        "userHistory": [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "G1234列车什么时候发车？"
            }
        ],
        "domain": "train"
    }
    
    train_result = service.fix_intent(
        gptcontent=train_example["gptcontent"],
        slot=train_example["slot"],
        userHistory=train_example["userHistory"],
        domain=train_example["domain"]
    )
    
    print("\n=== 列车域意图修正结果 ===")
    print(json.dumps(train_result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main() 