#!/bin/bash

# IntentFix 服务启动脚本

echo "🚀 启动 IntentFix Web API 服务..."

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ Python 未安装或不在PATH中"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
python -c "import fastapi, uvicorn, jieba, pydantic" 2>/dev/null || {
    echo "❌ 缺少依赖，请手动安装: pip install -r requirements-core.txt"
    echo "或者运行: pip install pydantic regex python-dateutil jieba fastapi uvicorn python-multipart"
    exit 1
}

# 启动服务（多进程生产模式）
HOST=${HOST:-0.0.0.0}
PORT=${PORT:-8100}
WORKERS=${WORKERS:-4}
LOG_LEVEL=${LOG_LEVEL:-info}

echo "🌐 启动服务... (host=${HOST} port=${PORT} workers=${WORKERS} log=${LOG_LEVEL})"
python server.py --host ${HOST} --port ${PORT} --workers ${WORKERS} --log-level ${LOG_LEVEL}