#!/bin/bash

# IntentFix 服务停止脚本
# 用法:
#   ./stop_server.sh                 # 尝试按默认端口(8100)停止
#   ./stop_server.sh 8000            # 指定端口停止
#   PORT=8000 ./stop_server.sh       # 通过环境变量指定端口

PORT_ARG="$1"
PORT="${PORT_ARG:-${PORT:-8100}}"

echo "🛑 停止 IntentFix Web API 服务 (端口: ${PORT})..."

# 1) 优先按端口查找并优雅停止
PIDS_BY_PORT=$(lsof -ti tcp:${PORT} 2>/dev/null)
if [ -n "$PIDS_BY_PORT" ]; then
  echo "找到监听端口 ${PORT} 的进程: $PIDS_BY_PORT"
  for PID in $PIDS_BY_PORT; do
    echo "发送 SIGTERM 到进程 $PID ..."
    kill -TERM "$PID" 2>/dev/null || true
  done
  sleep 2
  # 强制杀进程（若仍存活）
  for PID in $PIDS_BY_PORT; do
    if kill -0 "$PID" 2>/dev/null; then
      echo "进程 $PID 未退出，发送 SIGKILL..."
      kill -KILL "$PID" 2>/dev/null || true
    fi
  done
  echo "✅ 已停止端口 ${PORT} 的服务"
  exit 0
fi

# 2) 退而求其次：按进程命令匹配 uvicorn api.app:app 或 python server.py
PIDS_BY_CMD=$(ps aux | grep -E "uvicorn\s+api.app:app|python\s+server.py" | grep -v grep | awk '{print $2}')
if [ -n "$PIDS_BY_CMD" ]; then
  echo "按命令匹配到进程: $PIDS_BY_CMD"
  for PID in $PIDS_BY_CMD; do
    echo "发送 SIGTERM 到进程 $PID ..."
    kill -TERM "$PID" 2>/dev/null || true
  done
  sleep 2
  for PID in $PIDS_BY_CMD; do
    if kill -0 "$PID" 2>/dev/null; then
      echo "进程 $PID 未退出，发送 SIGKILL..."
      kill -KILL "$PID" 2>/dev/null || true
    fi
  done
  echo "✅ 已停止服务"
  exit 0
fi

echo "ℹ️ 未找到运行中的服务进程"
exit 0


