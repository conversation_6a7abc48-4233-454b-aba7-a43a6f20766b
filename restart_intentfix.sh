#!/bin/bash

# 检查是否提供了服务器模式参数
if [ $# -lt 1 ]; then
    echo "用法: $0 [test|online]"
    exit 1
fi

server_mode="$1"

# 根据服务器模式设置端口
if [ "$server_mode" = "test" ]; then
    port=8100
    ROOT_PATH="/data/search/project_python/intentfix"
    ENV_PATH="/home/<USER>/miniconda3/envs/intentfix/bin/python"
elif [ "$server_mode" = "online" ]; then
    port=8100
    ROOT_PATH="/data/search/project_python/intentfix"
    ENV_PATH="/root/miniconda3/envs/intentfix/bin/python"
else
    echo "请设置正确的服务器模式（test 或 online）。"
    exit 1
fi

while true; do
    # 使用 netstat 检查端口占用情况
    netstat_result=$(netstat -tunlp 2>/dev/null | grep ":$port ")

    if [ -n "$netstat_result" ]; then
        echo "端口 $port 被占用，正在尝试终止占用该端口的进程..."
        # 使用 awk 获取进程 ID
        pid=$(echo "$netstat_result" | awk '{print $7}' | cut -d'/' -f1)

        # 检查 PID 是否有效
        if [[ "$pid" =~ ^[0-9]+$ ]]; then
            echo "准备优雅关闭进程 $pid..."
            kill -9 "$pid"
        fi
    else
        echo "端口 $port 未被占用。"
    fi

    # 再次检查端口占用情况，确保端口已释放
    netstat_result=$(netstat -tunlp 2>/dev/null | grep ":$port ")

    if [ -z "$netstat_result" ]; then
        echo "正在启动程序..."
        # 启动程序并将输出重定向到日志文件
        nohup "$ENV_PATH" server.py --reload --host 0.0.0.0 --port $port >> "$ROOT_PATH/intentfix_$server_mode.log" 2>&1 &
        echo "程序已启动。"
        break
    fi

    # 等待一段时间后再次检查
    sleep 1
done

echo "脚本执行完毕。"
