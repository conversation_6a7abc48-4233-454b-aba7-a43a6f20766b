#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试火车车次号规则
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.rule_engine import RuleEngine
import re


def test_train_number_patterns():
    """测试火车车次号模式匹配"""
    
    rule_engine = RuleEngine()
    
    # 有效的车次号（根据Java规则）
    valid_train_numbers = [
        "G1234", "G54285", "D123", "D4567", "T123", "T4567",
        "K123", "K4567", "C123", "C4567", "Z123", "Z4567",
        "X123", "X4567", "Y123", "Y4567", "L123", "L4567"
    ]
    
    # 无效的车次号
    invalid_train_numbers = [
        "A123", "B123", "E123", "F123", "H123", "I123", "J123",
        "M123", "N123", "O123", "P123", "Q123", "R123", "S123",
        "U123", "V123", "W123"
    ]
    
    print("=" * 60)
    print("火车车次号规则测试")
    print("=" * 60)
    
    print("\n1. 测试有效的车次号:")
    print("-" * 40)
    for train_no in valid_train_numbers:
        # 测试车次号检测函数
        contains_train = rule_engine._contains_train_number(train_no)
        
        # 测试模式匹配
        pattern_result = rule_engine.pattern_matching(train_no)
        
        print(f"车次号: {train_no}")
        print(f"  包含车次号: {contains_train}")
        print(f"  模式匹配意图: {pattern_result.intent}")
        print(f"  模式匹配置信度: {pattern_result.confidence:.3f}")
        
        if pattern_result.matched_patterns:
            print(f"  匹配模式: {', '.join(pattern_result.matched_patterns)}")
        print()
    
    print("\n2. 测试无效的车次号:")
    print("-" * 40)
    for train_no in invalid_train_numbers:
        contains_train = rule_engine._contains_train_number(train_no)
        pattern_result = rule_engine.pattern_matching(train_no)
        
        print(f"车次号: {train_no}")
        print(f"  包含车次号: {contains_train}")
        print(f"  模式匹配意图: {pattern_result.intent}")
        print()


def test_train_queries():
    """测试包含车次号的查询"""
    
    rule_engine = RuleEngine()
    
    test_queries = [
        # 包含有效车次号的查询
        "G1234还有票吗",
        "D4567什么时候发车",
        "T123从北京到上海",
        "K4567二等座多少钱",
        "C123查询余票",
        "Z4567改签",
        "X123退票",
        "Y4567取消",
        "L123查询车次信息",
        
        # 包含无效车次号的查询
        "A123还有票吗",
        "B4567什么时候发车",
        "E123从北京到上海",
        
        # 复杂查询
        "明天G1234从北京到上海的二等座还有票吗",
        "后天D4567的票价是多少",
        "G54285什么时候到达",
        "查询K123的余票信息"
    ]
    
    print("\n3. 测试包含车次号的查询:")
    print("-" * 40)
    
    for query in test_queries:
        print(f"\n查询: {query}")
        
        # 应用所有规则
        result = rule_engine.apply_rules(query)
        
        print(f"  识别意图: {result.intent}")
        print(f"  置信度: {result.confidence:.3f}")
        
        if result.matched_keywords:
            print(f"  匹配关键词: {', '.join(result.matched_keywords)}")
        
        if result.matched_patterns:
            print(f"  匹配模式: {', '.join(result.matched_patterns)}")
        
        # 检查是否包含车次号
        contains_train = rule_engine._contains_train_number(query)
        print(f"  包含车次号: {contains_train}")


def test_regex_pattern():
    """测试正则表达式模式"""
    
    # 根据Java规则的正则表达式
    train_pattern = r'[GDTCKZXYL]\d{1,4}'
    
    valid_cases = [
        "G1234", "G54285", "D123", "D4567", "T123", "T4567",
        "K123", "K4567", "C123", "C4567", "Z123", "Z4567",
        "X123", "X4567", "Y123", "Y4567", "L123", "L4567"
    ]
    
    invalid_cases = [
        "A123", "B123", "E123", "F123", "H123", "I123", "J123",
        "M123", "N123", "O123", "P123", "Q123", "R123", "S123",
        "U123", "V123", "W123"
    ]
    
    print("\n4. 正则表达式模式测试:")
    print("-" * 40)
    
    print("有效车次号:")
    for case in valid_cases:
        match = re.search(train_pattern, case)
        print(f"  {case}: {'✓' if match else '✗'}")
    
    print("\n无效车次号:")
    for case in invalid_cases:
        match = re.search(train_pattern, case)
        print(f"  {case}: {'✓' if match else '✗'}")


if __name__ == "__main__":
    test_train_number_patterns()
    test_train_queries()
    test_regex_pattern()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60) 