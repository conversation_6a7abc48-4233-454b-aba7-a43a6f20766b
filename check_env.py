#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查脚本
检查IntentFix服务所需的所有依赖
"""

import sys
import subprocess

# 必需的依赖列表（暂时排除rasa）
REQUIRED_PACKAGES = [
    'pydantic',
    'regex',
    'python-dateutil',
    'jieba',
    'fastapi',
    'uvicorn',
    'python-multipart'
]

# 可选依赖（如果缺失会警告但不会阻止启动）
OPTIONAL_PACKAGES = [
    'scikit-learn',
    'numpy', 
    'pandas'
]

def check_package(package_name):
    """检查单个包是否已安装"""
    try:
        __import__(package_name.replace('-', '_'))
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def install_requirements():
    """安装requirements.txt中的依赖（排除rasa）"""
    try:
        # 先尝试安装核心依赖
        core_packages = [
            'pydantic>=2.0.0',
            'regex>=2023.0.0', 
            'python-dateutil>=2.8.0',
            'jieba>=0.42.0',
            'fastapi>=0.104.0',
            'uvicorn>=0.24.0',
            'python-multipart>=0.0.6'
        ]
        
        for package in core_packages:
            print(f"安装 {package}...")
            if not install_package(package):
                print(f"❌ 安装 {package} 失败")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def main():
    print("🔍 检查IntentFix环境依赖...")
    
    # 检查必需依赖
    missing_packages = []
    for package in REQUIRED_PACKAGES:
        if check_package(package):
            print(f"✅ {package}")
        else:
            print(f"❌ {package} - 缺失")
            missing_packages.append(package)
    
    # 检查可选依赖
    missing_optional = []
    for package in OPTIONAL_PACKAGES:
        if check_package(package):
            print(f"✅ {package} (可选)")
        else:
            print(f"⚠️  {package} - 缺失 (可选)")
            missing_optional.append(package)
    
    if missing_packages:
        print(f"\n📦 发现 {len(missing_packages)} 个缺失的必需依赖")
        print("正在安装依赖...")
        
        if install_requirements():
            print("✅ 依赖安装完成")
            
            # 重新检查必需依赖
            print("\n🔍 重新检查必需依赖...")
            still_missing = []
            for package in missing_packages:
                if check_package(package):
                    print(f"✅ {package} - 已安装")
                else:
                    print(f"❌ {package} - 仍然缺失")
                    still_missing.append(package)
            
            if still_missing:
                print(f"\n❌ 仍有 {len(still_missing)} 个必需依赖缺失: {', '.join(still_missing)}")
                return False
        else:
            print("❌ 依赖安装失败")
            return False
    
    if missing_optional:
        print(f"\n⚠️  可选依赖缺失: {', '.join(missing_optional)}")
        print("这些依赖不是必需的，但可能影响某些功能")
    
    print("\n🎉 所有必需依赖检查通过！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)