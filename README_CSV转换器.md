# CSV转JSONL转换器

这个工具用于将CSV格式的数据转换为训练用的JSONL格式。

## 文件说明

- `csv_to_jsonl_converter.py` - 完整版本的转换器，包含详细的错误处理和日志
- `csv_to_jsonl_simple.py` - 简化版本的转换器，方便快速修改和定制

## 使用方法

### 1. 准备数据

确保你的CSV文件包含以下列：
- `sessionId` - 会话ID
- `question` - 用户问题
- `now_time` - 当前时间
- `extract_result` - 提取结果（JSON字符串格式）

### 2. 运行转换

```bash
python csv_to_jsonl_simple.py
```

### 3. 自定义系统提示词

在 `csv_to_jsonl_simple.py` 文件中，找到 `system_prompt_template` 变量，修改为你需要的系统提示词。

当前模板包含：
- 角色定义
- 任务要求
- 回应准则
- 对话风格
- 问题处理流程

### 4. 输出格式

生成的JSONL文件格式如下：

```json
{
  "messages": [
    {
      "role": "system",
      "content": "系统提示词内容..."
    },
    {
      "role": "user", 
      "content": "用户问题"
    },
    {
      "role": "assistant",
      "content": "提取结果的JSON字符串"
    }
  ]
}
```

## 配置参数

在脚本中可以修改的参数：

- `csv_file_path` - 输入的CSV文件路径
- `output_file_path` - 输出的JSONL文件路径
- `system_prompt_template` - 系统提示词模板

## 注意事项

1. CSV文件应该使用UTF-8编码
2. 脚本会自动处理BOM字符
3. 相同sessionId的数据会被合并到同一个对话中
4. 提取结果会被解析为JSON格式作为助手的回复

## 示例

输入CSV数据：
```
sessionId,question,now_time,extract_result,check_result,reason
10001_20250807_114756,长沙到成都的航班有哪些,现在的日期是：2029年12月14日  星期五 周五,"{'dst': '成都', 'org': '长沙', 'intent': 'flights'}",正确,符合规则
```

输出JSONL数据：
```json
{
  "messages": [
    {
      "role": "system", 
      "content": "系统提示词...参考当前日期：现在的日期是：2029年12月14日  星期五 周五"
    },
    {
      "role": "user",
      "content": "长沙到成都的航班有哪些"
    },
    {
      "role": "assistant", 
      "content": "{\"dst\": \"成都\", \"org\": \"长沙\", \"intent\": \"flights\"}"
    }
  ]
}
``` 