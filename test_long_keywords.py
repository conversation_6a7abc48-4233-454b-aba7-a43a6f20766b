#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试长关键词泛化功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.rule_engine import RuleEngine
from core.models import RuleResult


def test_long_keywords():
    """测试长关键词匹配功能"""
    
    # 初始化规则引擎
    rule_engine = RuleEngine()
    
    # 测试用例
    test_queries = [
        # 机票相关长查询
        "机票多少钱",
        "机票余票",
        "机票多少钱，机票余票",
        "飞机票多少钱",
        "飞机票余票",
        "航班查询",
        "航班信息",
        "登机时间",
        "起飞时间",
        "机票改签",
        "机票退票",
        
        # 火车票相关长查询
        "火车票多少钱",
        "火车票余票", 
        "火车票多少钱，火车二等座还有票吗？",
        "高铁票多少钱",
        "高铁票余票",
        "动车票多少钱",
        "动车票余票",
        "二等座",
        "一等座",
        "商务座",
        "还有票吗",
        "还有余票吗",
        "还有座位吗",
        "车次查询",
        "车次信息",
        "发车时间",
        "火车票改签",
        "火车票退票",
        
        # 通用查询
        "价格查询",
        "价格多少",
        "多少钱",
        "费用查询",
        "时间查询",
        "时刻表",
        "班次查询",
        
        # 复杂组合查询
        "从北京到上海的机票多少钱",
        "从广州到深圳的高铁二等座还有票吗",
        "明天从成都到重庆的动车票价格",
        "后天从杭州到南京的航班余票查询"
    ]
    
    print("=" * 60)
    print("长关键词泛化测试")
    print("=" * 60)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n测试 {i}: {query}")
        print("-" * 40)
        
        # 应用规则
        result = rule_engine.apply_rules(query)
        
        print(f"识别意图: {result.intent}")
        print(f"置信度: {result.confidence:.3f}")
        
        if result.matched_keywords:
            print(f"匹配关键词: {', '.join(result.matched_keywords)}")
        
        if result.matched_patterns:
            print(f"匹配模式: {', '.join(result.matched_patterns)}")
        
        # 详细分析
        keyword_result = rule_engine.keyword_matching(query)
        pattern_result = rule_engine.pattern_matching(query)
        
        print(f"关键词匹配置信度: {keyword_result.confidence:.3f}")
        print(f"模式匹配置信度: {pattern_result.confidence:.3f}")


def test_keyword_weights():
    """测试关键词权重计算"""
    
    rule_engine = RuleEngine()
    
    test_cases = [
        ("机票", "flights"),
        ("机票多少钱", "flights"),
        ("机票余票", "flights"),
        ("火车票", "train"),
        ("火车票多少钱", "train"),
        ("火车二等座还有票吗", "train"),
    ]
    
    print("\n" + "=" * 60)
    print("关键词权重测试")
    print("=" * 60)
    
    for query, expected_intent in test_cases:
        result = rule_engine.keyword_matching(query)
        
        print(f"\n查询: {query}")
        print(f"期望意图: {expected_intent}")
        print(f"实际意图: {result.intent}")
        print(f"置信度: {result.confidence:.3f}")
        print(f"匹配关键词: {', '.join(result.matched_keywords)}")
        
        # 检查长关键词是否获得更高权重
        if len(query) > 4:
            print("✓ 长查询获得更高权重")


def test_pattern_matching():
    """测试模式匹配功能"""
    
    rule_engine = RuleEngine()
    
    test_cases = [
        ("机票多少钱", "flights"),
        ("机票余票", "flights"),
        ("火车票多少钱", "train"),
        ("火车二等座还有票吗", "train"),
        ("还有票吗", "normal"),
        ("价格查询", "normal"),
    ]
    
    print("\n" + "=" * 60)
    print("模式匹配测试")
    print("=" * 60)
    
    for query, expected_intent in test_cases:
        result = rule_engine.pattern_matching(query)
        
        print(f"\n查询: {query}")
        print(f"期望意图: {expected_intent}")
        print(f"实际意图: {result.intent}")
        print(f"置信度: {result.confidence:.3f}")
        if result.matched_patterns:
            print(f"匹配模式: {', '.join(result.matched_patterns)}")


if __name__ == "__main__":
    test_long_keywords()
    test_keyword_weights()
    test_pattern_matching()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60) 