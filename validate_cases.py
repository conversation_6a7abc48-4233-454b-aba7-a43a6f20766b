#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证脚本：集中回归验证历史修复用例

用法：
  python validate_cases.py               # 运行内置用例
  python validate_cases.py --verbose     # 打印详细入参/出参

扩展：
  - 直接在 CASES 列表中新增用例（建议复制现有结构进行修改）
  - 支持动态期望日期标记：TODAY / TOMORROW
"""

from __future__ import annotations

import argparse
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from main import IntentFixService


def resolve_expected_value(value: Any) -> Any:
    if value == "TODAY":
        return datetime.now().strftime("%Y-%m-%d")
    if value == "TOMORROW":
        return (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    if value in ("AFTER_TOMORROW", "DAY_AFTER_TOMORROW"):
        return (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d")
    return value


def expect_subset(actual: Dict[str, Any], expected_subset: Dict[str, Any]) -> (bool, List[str]):
    errors: List[str] = []
    for k, v in expected_subset.items():
        rv = resolve_expected_value(v)
        av = actual.get(k)
        if isinstance(rv, dict) and isinstance(av, dict):
            ok, sub_errors = expect_subset(av, rv)
            if not ok:
                errors.extend([f"{k}.{e}" for e in sub_errors])
        else:
            if av != rv:
                errors.append(f"{k}: expected={rv} actual={av}")
    return (len(errors) == 0), errors


# 回归用例库：新增时按如下结构添加
CASES: List[Dict[str, Any]] = [
    {
        "name": "tmall_flights_xian_changchun_default_today",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "arrival_time": "",
                    "dst": "长春",
                    "org": "西安",
                    "questions": [],
                    "cabin": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "airport": "",
                    "返回日期": "",
                    "max_price": 0,
                    "航班/车次号": "",
                    "min_price": 0,
                    "plane_type": "",
                    "出发日期": "2023-09-25",
                    "airline": "",
                    "departure_time": "",
                    "主业务类型": "机票"
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [{"role": "user", "content": "西安到长春的机票"}],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "西安",
                "dst": "长春",
                "出发日期": "TODAY",
                "主业务类型": "机票",
            }
        },
    },
    {
        "name": "tmall_train_xiamen_fuzhou_20231005",
        "request": {
            "gptcontent": {
                "intent": "train",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "福州",
                    "org": "厦门",
                    "questions": [],
                    "出发日期": "2023-10-05",
                    "itinerary_num": 1,
                    "intent": "train",
                    "主业务类型": "火车票",
                    "airport": ""
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [{"role": "user", "content": "厦门到福州的火车票"}],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "train",
                "org": "厦门",
                "dst": "福州",
                "出发日期": "TODAY",
                "主业务类型": "火车票",
            }
        },
    },
    {
        "name": "tmall_flights_no_time_defaults_today",
        "request": {
            "gptcontent": {"intent": "flights", "parameters": {"org": "北京", "dst": "上海", "出发日期": ""}},
            "slot": {"intent": "flights", "slots": {}},
            "userHistory": [{"role": "user", "time": datetime.now().isoformat(), "type": "text", "content": "从北京到上海的航班"}],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "北京",
                "dst": "上海",
                "出发日期": "TODAY",
            }
        },
    },
    {
        "name": "tmall_flights_query_tomorrow",
        "request": {
            "gptcontent": {"intent": "flights", "parameters": {"org": "北京", "dst": "上海", "出发日期": ""}},
            "slot": {"intent": "flights", "slots": {}},
            "userHistory": [{"role": "user", "time": datetime.now().isoformat(), "type": "text", "content": "明天从北京到上海的航班"}],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "北京",
                "dst": "上海",
                "出发日期": "TOMORROW",
            }
        },
    },
    {
        "name": "tmall_train_history_tomorrow",
        "request": {
            "gptcontent": {"intent": "train", "parameters": {"org": "上海", "dst": "北京", "出发日期": ""}},
            "slot": {"intent": "train", "slots": {"msg": "到北京的火车有哪些呢", "dst": "北京", "org": "上海"}},
            "userHistory": [
                {"role": "user", "time": datetime.now().isoformat(), "type": "text", "content": "到北京的火车有哪些呢"},
                {"role": "assistant", "time": datetime.now().isoformat(), "type": "text", "content": "暂无上海至北京的列车信息，请关注后续车次安排。"},
                {"role": "user", "time": datetime.now().isoformat(), "type": "text", "content": "明天的呢"},
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "train",
                "org": "上海",
                "dst": "北京",
                "出发日期": "TOMORROW",
            }
        },
    },
    {
        "name": "tmall_train_to_flight_switch",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "arrival_time": "",
                    "dst": "",
                    "org": "",
                    "questions": [],
                    "cabin": "",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "airport": "",
                    "返回日期": "",
                    "max_price": 0,
                    "航班/车次号": "DZ6228",
                    "min_price": 0,
                    "plane_type": "",
                    "出发日期": "2025-08-12",
                    "airline": "DZ",
                    "departure_time": "",
                    "主业务类型": "机票"
                }
            },
            "slot": {
                "intent": "train",
                "slots": {
                    "msg": "G40晚点了吗？",
                    "航班/车次号": "G40",
                    "phoneid": "",
                    "questions": "[]",
                    "出发日期": "2025-08-12",
                    "sessionId": "43114083",
                    "itinerary_num": "1",
                    "intent": "train",
                    "主业务类型": "机票"
                }
            },
            "userHistory": [
                {"role": "user", "content": "G40晚点了吗？"},
                {"role": "assistant", "content": "未查询到相关火车信息，请提供出发日期和车次号重新查询"},
                {"role": "user", "content": "DZ6228几点起飞？"}
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "航班/车次号": "DZ6228",
                "出发日期": "TODAY",
                "主业务类型": "机票",
            }
        },
    },
    {
        "name": "tmall_jd5217_flight_number",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "arrival_time": "",
                    "dst": "",
                    "org": "",
                    "questions": [],
                    "cabin": "",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "airport": "",
                    "返回日期": "",
                    "max_price": 0,
                    "航班/车次号": "",
                    "min_price": 0,
                    "plane_type": "",
                    "出发日期": "2025-08-12",
                    "airline": "",
                    "departure_time": "",
                    "主业务类型": "机票"
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [{"role": "user", "content": "JD5217是什么航空公司"}],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "航班/车次号": "JD5217",
                "出发日期": "TODAY",
                "主业务类型": "机票",
            }
        },
    },
    {
        "name": "tmall_cz8856_southern_airlines",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "arrival_time": "",
                    "dst": "",
                    "org": "",
                    "questions": [],
                    "cabin": "",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "airport": "",
                    "返回日期": "",
                    "max_price": 0,
                    "航班/车次号": "",
                    "min_price": 0,
                    "plane_type": "",
                    "出发日期": "2025-08-12",
                    "airline": "",
                    "departure_time": "",
                    "主业务类型": "机票"
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [{"role": "user", "content": "南航CZ8856"}],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "航班/车次号": "CZ8856",
                "出发日期": "TODAY",
                "主业务类型": "机票",
            }
        },
    },
    {
        "name": "tmall_flight_to_train_switch",
        "request": {
            "gptcontent": {
                "intent": "train",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "济南",
                    "org": "北京",
                    "questions": [],
                    "出发日期": "2025-08-14",
                    "itinerary_num": "1",
                    "intent": "train",
                    "主业务类型": "火车票",
                    "airport": ""
                }
            },
            "slot": {
                "intent": "flights",
                "slots": {
                    "msg": "后天北京飞济南的机票多少钱？",
                    "dst": "济南",
                    "org": "北京",
                    "phoneid": "",
                    "questions": "[]",
                    "sessionId": "43114025",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "max_price": "0",
                    "min_price": "0",
                    "出发日期": "2025-08-14",
                    "主业务类型": "机票"
                }
            },
            "userHistory": [
                {"role": "user", "content": "后天北京飞济南的机票多少钱？"},
                {"role": "assistant", "content": "未查询到相关航班信息，请提供出发日期和航班号重新查询"},
                {"role": "user", "content": "如果是坐高铁，要多少钱？"}
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "train",
                "org": "北京",
                "dst": "济南",
                "出发日期": "AFTER_TOMORROW",
                "主业务类型": "火车票",
            }
        },
    },
    {
        "name": "tmall_flight_price_followup",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "长春",
                    "org": "无锡",
                    "questions": [],
                    "出发日期": "2025-08-14",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "主业务类型": "机票"
                }
            },
            "slot": {
                "intent": "flights",
                "slots": {
                    "msg": "请问明天无锡到长春的机票价格是多少？",
                    "dst": "长春",
                    "org": "无锡",
                    "phoneid": "",
                    "questions": "[]",
                    "sessionId": "43114029",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "max_price": "0",
                    "min_price": "0",
                    "出发日期": "2025-08-13",
                    "主业务类型": "机票"
                }
            },
            "userHistory": [
                {"role": "user", "content": "请问明天无锡到长春的机票价格是多少？"},
                {"role": "assistant", "content": "明天硕放至龙嘉航班最低1570元起。"},
                {"role": "user", "content": "那后天呢？价格多少钱？"}
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "无锡",
                "dst": "长春",
                "出发日期": "AFTER_TOMORROW",
                "主业务类型": "机票",
            }
        },
    },
    {
        "name": "tmall_train_d36_with_context_change_city",
        "request": {
            "gptcontent": {
                "intent": "train",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "D36",
                    "dst": "乌鲁木齐",
                    "org": "武汉",
                    "questions": [],
                    "出发日期": "2025-08-14",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "train",
                    "主业务类型": "火车票",
                    "airport": ""
                }
            },
            "slot": {
                "intent": "train",
                "slots": {
                    "msg": "D36 后天出发时间",
                    "航班/车次号": "D36",
                    "dst": "乌鲁木齐",
                    "org": "武汉",
                    "phoneid": "",
                    "questions": "[]",
                    "出发日期": "2025-08-14",
                    "sessionId": "43114053",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "train",
                    "主业务类型": "火车票"
                }
            },
            "userHistory": [
                {"role": "user", "content": "武汉飞乌鲁木齐飞多长时间？"},
                {"role": "assistant", "content": "武汉飞乌鲁木齐飞行时长4小时25分起。"},
                {"role": "user", "content": "机票现在卖多少钱？"},
                {"role": "assistant", "content": "您好，武汉飞乌鲁木齐机票最低1660元起。"},
                {"role": "user", "content": "D36 后天出发时间"},
                {"role": "assistant", "content": "未查询到相关火车信息，请提供出发日期和车次号重新查询"},
                {"role": "user", "content": "从郑州上车是几点出发？"}
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "train",
                "org": "武汉",
                "dst": "乌鲁木齐",
                "出发日期": "AFTER_TOMORROW",
                "航班/车次号": "D36",
                "主业务类型": "火车票",
            }
        },
    },
    {
        "name": "tmall_flights_chengdu_kunming_cabin_price_followup",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "arrival_time": "",
                    "dst": "昆明",
                    "org": "成都",
                    "questions": [],
                    "cabin": "Y",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "airport": "",
                    "返回日期": "",
                    "max_price": "0",
                    "航班/车次号": "",
                    "min_price": "0",
                    "plane_type": "",
                    "出发日期": "2025-08-13",
                    "airline": "",
                    "departure_time": "",
                    "主业务类型": "机票"
                }
            },
            "slot": {
                "intent": "flights",
                "slots": {
                    "msg": "明天成都飞昆明的机票有哪些？",
                    "dst": "昆明",
                    "org": "成都",
                    "phoneid": "",
                    "questions": "[]",
                    "sessionId": "43114059",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "max_price": "0",
                    "min_price": "0",
                    "出发日期": "2025-08-13",
                    "主业务类型": "机票"
                }
            },
            "userHistory": [
                {"role": "user", "content": "明天成都飞昆明的机票有哪些？"},
                {"role": "assistant", "content": "您好，2025年8月12日成都飞昆明有多趟航班，最低票价500元起。"},
                {"role": "user", "content": "经济舱的价格是多少？"}
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "成都",
                "dst": "昆明",
                "出发日期": "TOMORROW",
                "主业务类型": "机票",
            }
        },
    },
    {
        "name": "tmall_beijing_to_nanning_price_should_be_train",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "南宁",
                    "org": "北京",
                    "questions": [],
                    "出发日期": "2023-10-05",
                    "itinerary_num": 1,
                    "intent": "flights",
                    "主业务类型": "火车票",
                    "airport": ""
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [{"role": "user", "content": "北京去南宁多少钱"}],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "train",
                "org": "北京",
                "dst": "南宁",
                "出发日期": "TODAY",
                "主业务类型": "火车票",
            }
        },
    },
    {
        "name": "tmall_train_xinyi_to_lianyungang_20250820_earliest",
        "request": {
            "gptcontent": {
                "intent": "train",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "连云港",
                    "org": "新沂",
                    "questions": [],
                    "出发日期": "2025-08-20",
                    "itinerary_num": "1",
                    "intent": "train",
                    "主业务类型": "火车票",
                    "airport": ""
                }
            },
            "slot": {
                "intent": "train",
                "slots": {
                    "msg": "8月20号 新沂到连云港最早的一班火车票 是几点的",
                    "dst": "连云港",
                    "org": "新沂",
                    "phoneid": "",
                    "questions": "[]",
                    "出发日期": "2025-08-13",
                    "sessionId": "44010040",
                    "itinerary_num": "1",
                    "intent": "train",
                    "主业务类型": "火车票"
                }
            },
            "userHistory": [
                {"role": "user", "content": "8月20号 新沂到连云港最早的一班火车票 是几点的"},
                {"role": "assistant", "content": "请提供出发日期与车次号以便查询。"},
                {"role": "user", "content": "8月20号 新沂到连云港最早的一班火车票 是几点的"}
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "train",
                "org": "新沂",
                "dst": "连云港",
                "出发日期": "2025-08-20",
                "主业务类型": "火车票",
            }
        },
    },
    {
        "name": "tmall_yibin_guangzhou_0911_date_extract",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "dst": "广州",
                    "org": "宜宾",
                    "出发日期": "",
                    "intent": "flights"
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [
                {"role": "user", "content": "9.11四川宜宾-广州"}
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "train",
                "org": "宜宾",
                "dst": "广州",
                "出发日期": "2025-09-11",
                "主业务类型": "火车票"
            }
        },
    },
    {
        "name": "tmall_beijing_doha_0820_0640_date_extract",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "dst": "多哈",
                    "org": "北京",
                    "出发日期": "",
                    "intent": "flights"
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [
                {"role": "user", "content": "8月20 6点40 北京飞多哈"}
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "北京",
                "dst": "多哈",
                "出发日期": "2025-08-20"
            }
        },
    },
    {
        "name": "tmall_first_turn_no_city_no_number_defaults_normal",
        "request": {
            "gptcontent": {
                "intent": "normal",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "",
                    "org": "",
                    "questions": [
                        "军人购买机票是否有折扣优惠？",
                        "现役军人买飞机票有优惠吗？",
                        "军人购票是否有特殊票价政策？",
                        "部队人员坐飞机有票价减免吗？",
                        "军人乘坐民航航班有优惠待遇吗？"
                    ],
                    "出发日期": "",
                    "itinerary_num": 1,
                    "主业务类型": "机票",
                    "intent": "normal",
                    "airport": ""
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [
                {"role": "user", "content": "军人购买机票有优惠吗?\n</br>"}
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "normal"
            }
        },
    },
    {
        "name": "tmall_airport_terminal_info_query",
        "request": {
            "gptcontent": {
                "intent": "normal",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "",
                    "org": "",
                    "questions": [
                        "白云机场T2航站楼A区和B区有什么不同？",
                        "广州白云机场T2航站楼A区与B区的功能区分是什么？",
                        "在白云机场T2航站楼，A区和B区分别有哪些航空公司使用？",
                        "白云机场T2的A区和B区在旅客服务设施上有何差异？",
                        "从出行便利性角度看，白云机场T2航站楼A区和B区有什么区别？"
                    ],
                    "出发日期": "",
                    "itinerary_num": 1,
                    "主业务类型": "机票",
                    "intent": "normal",
                    "airport": "白云机场T2航站楼"
                }
            },
            "slot": {
                "intent": "flights",
                "slots": {
                    "msg": "上海",
                    "返回日期": "2025-08-11",
                    "dst": "上海",
                    "org": "上海",
                    "phoneid": "182497968",
                    "questions": "[]",
                    "出发日期": "2025-08-11",
                    "sessionId": "123456",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "主业务类型": "机票"
                }
            },
            "userHistory": [
                {"role": "user", "content": "到北京的航班"},
                {"role": "assistant", "content": "请提供出发地信息"},
                {"role": "user", "content": "上海"},
                {"role": "assistant", "content": "未找到相关行程。"},
                {"role": "user", "content": "白云机场T2航站楼A区和B区的区别"}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "normal",
                "airport": "白云机场",
                "questions": [
                    "白云机场T2航站楼A区和B区有什么不同？",
                    "广州白云机场T2航站楼A区与B区的功能区分是什么？",
                    "在白云机场T2航站楼，A区和B区分别有哪些航空公司使用？",
                    "白云机场T2的A区和B区在旅客服务设施上有何差异？",
                    "从出行便利性角度看，白云机场T2航站楼A区和B区有什么区别？"
                ],
                "主业务类型": "机票",
                "org": "",
                "dst": "",
                "出发日期": "",
                "返回日期": "",
                "航班/车次号": ""
            }
        }
    },
    {
        "name": "tmall_flight_cz3850_plane_type_query",
        "request": {
            "gptcontent": {
                "intent": "normal",
                "parameters": {
                    "arrival_time": "",
                    "dst": "",
                    "org": "",
                    "questions": [
                        "CZ3850航班使用的是大型飞机吗？",
                        "CZ3850航班执飞的机型是大机型还是小机型？",
                        "南航CZ3850航班通常用什么型号的飞机执飞？",
                        "CZ3850航班的飞机大小如何？",
                        "CZ3850是宽体机还是窄体机？"
                    ],
                    "cabin": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "intent": "normal",
                    "airport": "",
                    "返回日期": "",
                    "航班/车次号": "CZ3850",
                    "max_price": 0,
                    "min_price": 0,
                    "plane_type": "",
                    "出发日期": "",
                    "airline": "",
                    "主业务类型": "机票",
                    "departure_time": ""
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [{"role": "user", "content": "CZ3850是大飞机还是小飞机"}],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "航班/车次号": "CZ3850",
                "主业务类型": "机票",
                "出发日期": "TODAY"
            }
        }
    },
    {
        "name": "tmall_train_g1277_wifi_query",
        "request": {
            "gptcontent": {
                "intent": "normal",
                "parameters": {
                    "arrival_time": "",
                    "dst": "",
                    "org": "",
                    "questions": [
                        "G1277次列车提供WiFi吗？",
                        "乘坐G1277次高铁有无线网络可以使用吗？",
                        "G1277次动车组是否配备上网服务？",
                        "在G1277列车上能否连接Wi-Fi？",
                        "G1277次车有没有提供车上互联网服务？"
                    ],
                    "cabin": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "intent": "normal",
                    "airport": "",
                    "返回日期": "",
                    "航班/车次号": "G1277",
                    "max_price": 0,
                    "min_price": 0,
                    "plane_type": "",
                    "出发日期": "2023-10-06",
                    "airline": "",
                    "主业务类型": "火车票",
                    "departure_time": ""
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [{"role": "user", "content": "G1277有网吗？"}],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "train",
                "航班/车次号": "G1277",
                "主业务类型": "火车票",
                "出发日期": "TODAY"
            }
        }
    },
    {
        "name": "tmall_flight_mu9226_meal_query",
        "request": {
            "gptcontent": {
                "intent": "normal",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "MU9226",
                    "dst": "",
                    "org": "",
                    "questions": [
                        "MU9226航班提供什么餐食",
                        "东航MU9226航班有餐食服务吗",
                        "东方航空MU9226航班的餐食标准是什么",
                        "MU9226航班经济舱餐食内容是什么",
                        "乘坐MU9226航班可以自带食物吗"
                    ],
                    "出发日期": "",
                    "itinerary_num": 1,
                    "主业务类型": "机票",
                    "intent": "normal",
                    "airport": ""
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [{"role": "user", "content": "mu9226是什么餐"}],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "航班/车次号": "MU9226",
                "主业务类型": "机票",
                "出发日期": "TODAY"
            }
        }
    },
    {
        "name": "tmall_airport_transit_layover_service_query",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "",
                    "org": "",
                    "questions": [],
                    "出发日期": "2025-08-20",
                    "itinerary_num": 1,
                    "intent": "flights",
                    "主业务类型": "机票",
                    "airport": ""
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [
                {"role": "user", "content": "在中转机场停留16个小时。机场或航司有什么安排"}
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "normal",
                "questions": [
                    "中转机场停留16小时有什么服务安排？",
                    "长时间中转停留机场提供哪些便民服务？",
                    "航空公司对长时间中转旅客有什么安排？",
                    "机场中转区有休息或住宿服务吗？",
                    "中转停留期间可以享受哪些免费服务？"
                ],
                "主业务类型": "机票",
                "org": "",
                "dst": "",
                "出发日期": "",
                "返回日期": "",
                "航班/车次号": ""
            }
        },
    },
    {
        "name": "tmall_senior_discount_high_speed_rail_question",
        "request": {
            "gptcontent": {
                "intent": "train",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "",
                    "org": "",
                    "questions": [],
                    "出发日期": "2025-08-20",
                    "itinerary_num": 1,
                    "intent": "train",
                    "主业务类型": "火车票",
                    "airport": "",
                    "departure_time": "",
                    "airline": ""
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [
                {"role": "user", "content": "高铁优惠的年龄起点"},
                {"role": "assistant", "content": "高铁儿童票的优惠年龄起点是年满6周岁。具体来说，乘坐高铁时，儿童的购票规则和优惠政策是根据年龄来划分的：- **未满6周岁**：可以免费乘车，但必须由持票的成年人携带，且不单独占用席位。每名持票成年人只能免费带一名未满6周岁的儿童，超过一名的话，超出的儿童需要购买儿童优惠票。- **年满6周岁且未满14周岁**：应当购买儿童优惠票。儿童优惠票可享受票价优惠，动车组列车（包括高铁）的儿童票按对应座位公布票价的50%计算。比如，如果二等座成人票是100元，儿童票就是50元。- **年满14周岁及以上**：需要购买全价票，不再享受儿童优惠。需要注意的是，儿童年龄是按乘车当日的实际年龄来计算的。购买儿童票时，必须使用儿童本人的有效身份证件进行购票和进站乘车。另外，如果是免费乘车的儿童，虽然不用买票，但也需要在购票时进行免费乘车儿童的申报，以便铁路系统登记信息，确保顺利进站乘车。所以，高铁票享受优惠的最低年龄起点是**6周岁**。不满6岁可免票，6岁到14岁之间可享半价优惠。"},
                {"role": "user", "content": "6 O岁以上老人购高铁票有优惠吗？"}
            ],
            "domain": "tmall",
        },
        "expected": {
            "gptcontent": {
                "intent": "normal",
                "questions": [
                    "60岁以上老人购买高铁票有优惠吗？",
                    "老年人乘坐高铁有票价优惠政策吗？",
                    "高铁对老年乘客有特殊票价吗？",
                    "60岁以上老人坐高铁能享受折扣吗？",
                    "老年人高铁票价有减免政策吗？"
                ],
                "主业务类型": "火车票",
                "org": "",
                "dst": "",
                "出发日期": "",
                "返回日期": "",
                "航班/车次号": ""
            }
        },
    },
    {
        "name": "tmall_jd5128_flight_query",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "主业务类型": "机票",
                    "intent": "flights",
                    "questions": [],
                    "航班/车次号": "JD5128",
                    "出发日期": "2025-08-19",
                    "org": "",
                    "dst": "",
                    "返回日期": "",
                    "airport": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc"
                }
            },
            "slot": {},
            "userHistory": [
                {"role": "user", "content": "JD5128"},
                {"role": "assistant", "content": ""}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "航班/车次号": "JD5128",
                "出发日期": "TODAY",
                "主业务类型": "机票"
            }
        }
    },
    {
        "name": "tmall_flights_shanghai_morning_departure",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "arrival_time": "",
                    "dst": "",
                    "org": "上海",
                    "questions": [],
                    "cabin": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "airport": "",
                    "返回日期": "",
                    "max_price": 0,
                    "航班/车次号": "",
                    "min_price": 0,
                    "plane_type": "",
                    "出发日期": "2025-08-21",
                    "airline": "",
                    "departure_time": "06:00-12:00",
                    "主业务类型": "机票"
                }
            },
            "slot": {
                "intent": "flights",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "上海",
                    "org": "上海",
                    "questions": [],
                    "出发日期": "2025-08-19",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "主业务类型": "机票",
                    "airport": ""
                },
                "org": "上海",
                "dst": "上海",
                "主业务类型": "机票",
                "出发日期": "2025-08-19",
                "airport": "",
                "questions": []
            },
            "userHistory": [
                {"role": "user", "content": "上海的航班有哪些"},
                {"role": "assistant", "content": ""},
                {"role": "user", "content": "后天早上出发的"},
                {"role": "assistant", "content": ""}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "上海",
                "dst": "上海",
                "出发日期": "AFTER_TOMORROW",
                "departure_time": "06:00-12:00",
                "主业务类型": "机票"
            }
        }
    },
    {
        "name": "tmall_train_gaotie_belongs_to_train_query",
        "request": {
            "gptcontent": {
                "intent": "train",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "厦门",
                    "org": "福州",
                    "questions": [],
                    "出发日期": "2025-08-19",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "train",
                    "主业务类型": "火车票",
                    "airport": ""
                }
            },
            "slot": {
                "intent": "train",
                "slots": {
                    "msg": "福清到夏门",
                    "dst": "厦门",
                    "org": "福州",
                    "phoneid": "",
                    "questions": "[]",
                    "sessionId": "bfb574c7-3eba-4910-bc32-f5271ffcc92f",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "train",
                    "max_price": "0",
                    "min_price": "0",
                    "出发日期": "2025-08-19",
                    "主业务类型": "火车票"
                }
            },
            "userHistory": [
                {"role": "user", "content": "9月1的铁票"},
                {"role": "assistant", "content": "学生优惠票可全年购买，但需在10月1日至次年9月30日使用。建议通过12306官网或APP查询具体车次是否支持学生票及余票情况。"},
                {"role": "user", "content": "上海到北京"},
                {"role": "assistant", "content": "上海到北京有多个车次，最快4时33分，最便宜152.5元"},
                {"role": "user", "content": "福清到夏门"},
                {"role": "assistant", "content": "未查询到相关火车信息，请提供出发日期和车次号重新查询"},
                {"role": "user", "content": "高铁属于火车吗"}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "normal",
                "questions": [
                    "高铁和火车有什么区别？",
                    "高铁是否属于火车的一种？",
                    "高速铁路与普通铁路的分类关系是什么？",
                    "高铁在交通工具分类中属于哪一类？",
                    "高铁和动车、普通火车的关系是什么？"
                ]
            }
        }
    },
    {
        "name": "tmall_flight_mu9226_meal_followup_query",
        "request": {
            "gptcontent": {
                "intent": "normal",
                "parameters": {
                    "arrival_time": "",
                    "dst": "",
                    "org": "",
                    "questions": [
                        "MU9226航班提供什么餐食",
                        "MU9226航班的餐饮服务包含哪些内容",
                        "乘坐MU9226航班有机上餐食吗",
                        "东航MU9226航班的餐食标准是什么",
                        "MU9226航班是否提供免费餐食"
                    ],
                    "cabin": "",
                    "itinerary_num": "1",
                    "sort": "departure_time_asc",
                    "intent": "normal",
                    "airport": "",
                    "返回日期": "",
                    "航班/车次号": "MU9226",
                    "max_price": 0,
                    "min_price": 0,
                    "plane_type": "",
                    "出发日期": "2024-07-18",
                    "airline": "",
                    "主业务类型": "机票",
                    "departure_time": ""
                }
            },
            "slot": {
                "intent": "flights",
                "slots": {
                    "msg": "mu9226是什么餐",
                    "航班/车次号": "MU9226",
                    "phoneid": "",
                    "questions": "[\"MU9226航班提供什么餐食\",\"MU9226航班的餐饮服务包含哪些内容\",\"乘坐MU9226航班有机上餐食吗\",\"东航MU9226航班的餐食标准是什么\",\"MU9226航班是否提供免费餐食\"]",
                    "出发日期": "2025-08-14",
                    "sessionId": "29925780-04c9-40a5-b35b-ce8b0f61412e",
                    "itinerary_num": "1",
                    "主业务类型": "机票",
                    "intent": "flights"
                }
            },
            "userHistory": [
                {"role": "user", "content": "mu9226是什么餐"},
                {"role": "assistant", "content": "未查询到相关航班信息，请提供出发日期和航班号重新查询"},
                {"role": "user", "content": "明天的"}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "航班/车次号": "MU9226",
                "主业务类型": "机票",
                "出发日期": "TOMORROW"
            }
        }
    },
    {
        "name": "tmall_flight_shanghai_beijing_morning_departure_followup",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "",
                    "org": "上海",
                    "questions": [],
                    "出发日期": "2025-08-19",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "主业务类型": "机票",
                    "airport": ""
                }
            },
            "slot": {},
            "userHistory": [
                {"role": "user", "content": "上海的航班有哪些"},
                {"role": "assistant", "content": ""}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "上海",
                "主业务类型": "机票",
                "出发日期": "TODAY"
            }
        }
    },
    {
        "name": "tmall_flight_number_sequence_followup",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "3U8925",
                    "dst": "",
                    "org": "",
                    "questions": [],
                    "出发日期": "2025-08-21",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "主业务类型": "机票",
                    "airport": ""
                }
            },
            "slot": {
                "intent": "flights",
                "parameters": {
                    "arrival_time": "",
                    "dst": "",
                    "org": "",
                    "questions": [],
                    "cabin": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "airport": "",
                    "返回日期": "",
                    "max_price": 0,
                    "航班/车次号": "MU9787",
                    "min_price": 0,
                    "plane_type": "",
                    "出发日期": "2025-08-19",
                    "airline": "MU",
                    "departure_time": "",
                    "主业务类型": "机票"
                },
                "org": "",
                "dst": "",
                "主业务类型": "机票",
                "出发日期": "2025-08-19",
                "航班/车次号": "MU9787",
                "airport": "",
                "questions": []
            },
            "userHistory": [
                {"role": "user", "content": "3U8925"},
                {"role": "assistant", "content": ""},
                {"role": "user", "content": "Mu5584"},
                {"role": "assistant", "content": ""},
                {"role": "user", "content": "厦门航空MU9787。"},
                {"role": "assistant", "content": ""},
                {"role": "user", "content": "后天的呢？"}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "航班/车次号": "MU9787",
                "出发日期": "AFTER_TOMORROW",
                "主业务类型": "机票"
            }
        }
    },
    {
        "name": "tmall_airport_t2_transfer_baggage_checkin_query",
        "request": {
            "gptcontent": {
                "intent": "normal",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "",
                    "org": "",
                    "questions": [
                        "T2航站楼的中转区是否有托运行李的旅客办理登机手续的专用通道？",
                        "在T2一层的中转区，托运行李旅客需要哪些手续才能登机？",
                        "T2航站楼中转区是否为托运行李旅客提供标准化登机服务？",
                        "在T2一层的中转区办理托运行李登机手续需要哪些证件？",
                        "T2航站楼中转区是否支持托运行李旅客的快速登机手续办理？"
                    ],
                    "出发日期": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "intent": "normal",
                    "主业务类型": "机票",
                    "airport": "T2"
                }
            },
            "slot": {},
            "userHistory": [
                {"role": "user", "content": "在T2一层的中转区，有托运行李的旅客可以办理哪些登机手续？"},
                {"role": "assistant", "content": ""}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "normal",
                "airport": "T2",
                "questions": [
                    "T2航站楼的中转区是否有托运行李的旅客办理登机手续的专用通道？",
                    "在T2一层的中转区，托运行李旅客需要哪些手续才能登机？",
                    "T2航站楼中转区是否为托运行李旅客提供标准化登机服务？",
                    "在T2一层的中转区办理托运行李登机手续需要哪些证件？",
                    "T2航站楼中转区是否支持托运行李旅客的快速登机手续办理？"
                ],
                "主业务类型": "机票",
                "出发日期": ""
            }
        }
    },
    {
        "name": "tmall_extract_past_date_auto_next_year",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "主业务类型": "机票",
                    "intent": "flights",
                    "questions": [],
                    "航班/车次号": "",
                    "出发日期": "",
                    "org": "昆明",
                    "dst": "杭州",
                    "返回日期": "",
                    "airport": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc"
                }
            },
            "slot": {},
            "userHistory": [
                {"role": "user", "content": "2月1日昆明到杭州的最早航班"},
                {"role": "assistant", "content": ""}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "昆明",
                "dst": "杭州",
                "出发日期": "2026-02-01",
                "主业务类型": "机票"
            }
        }
    },
    {
        "name": "tmall_flight_delay_inquiry_sequence",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "9C9980",
                    "dst": "",
                    "org": "",
                    "questions": [],
                    "出发日期": "2025-08-19",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "airline": "9C",
                    "intent": "flights",
                    "主业务类型": "机票",
                    "airport": ""
                }
            },
            "slot": {
                "intent": "flights",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "3U8005",
                    "dst": "",
                    "org": "",
                    "questions": [],
                    "出发日期": "2025-08-19",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "airline": "3U",
                    "intent": "flights",
                    "主业务类型": "机票",
                    "airport": ""
                },
                "org": "",
                "dst": "",
                "主业务类型": "机票",
                "出发日期": "2025-08-19",
                "airport": "",
                "questions": []
            },
            "userHistory": [
                {"role": "user", "content": "3u8005"},
                {"role": "assistant", "content": ""},
                {"role": "user", "content": "9C9980晚点了吗？"},
                {"role": "assistant", "content": ""}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "航班/车次号": "9C9980",
                "airline": "9C",
                "出发日期": "TODAY",
                "主业务类型": "机票"
            }
        }
    },
    {
        "name": "tmall_kuche_to_guangdong_train",
        "request": {
            "gptcontent": {
                "intent": "train",
                "parameters": {
                    "主业务类型": "火车票",
                    "intent": "train",
                    "questions": [],
                    "航班/车次号": "",
                    "出发日期": "2025-08-19",
                    "org": "库车",
                    "dst": "广州",
                    "返回日期": "",
                    "airport": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc"
                }
            },
            "slot": {},
            "userHistory": [
                {"role": "user", "content": "库车到广东"},
                {"role": "assistant", "content": ""}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "train",
                "org": "库车",
                "dst": "广州",
                "出发日期": "TODAY",
                "主业务类型": "火车票"
            }
        }
    },
    {
        "name": "tmall_normal_ticket_change_fee_inquiry",
        "request": {
            "gptcontent": {
                "intent": "normal",
                "parameters": {
                    "主业务类型": "机票",
                    "intent": "normal",
                    "questions": [
                        "为什么不同航班改期的费用不一样？",
                        "机票改期手续费为何会有差异？",
                        "更改航班日期时，为什么有的贵有的便宜？",
                        "不同的航空公司改期费用标准不一样吗？",
                        "同一航线改期费用为何因票种而异？"
                    ],
                    "航班/车次号": "",
                    "出发日期": "",
                    "org": "",
                    "dst": "",
                    "返回日期": "",
                    "airport": "",
                    "itinerary_num": 1,
                    "airline": "",
                    "departure_time": "",
                    "arrival_time": "",
                    "cabin": "",
                    "min_price": 0,
                    "max_price": 0,
                    "plane_type": "",
                    "sort": "departure_time_asc"
                }
            },
            "slot": {},
            "userHistory": [
                {"role": "user", "content": "为什么不同机票改期的费用不一样？"},
                {"role": "assistant", "content": ""}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "normal",
                "主业务类型": "机票"
            }
        }
    },
    {
        "name": "tmall_flights_guangzhou_changsha_multi_turn",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "主业务类型": "机票",
                    "intent": "flights",
                    "questions": [],
                    "航班/车次号": "",
                    "出发日期": "2025-08-19",
                    "org": "广州",
                    "dst": "长沙",
                    "返回日期": "",
                    "airport": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc"
                }
            },
            "slot": {
                "intent": "flights",
                "parameters": {
                    "主业务类型": "机票",
                    "intent": "flights",
                    "questions": [],
                    "航班/车次号": "",
                    "出发日期": "2025-08-19",
                    "org": "",
                    "dst": "上海",
                    "返回日期": "",
                    "airport": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc"
                },
                "org": "",
                "dst": "上海",
                "主业务类型": "机票",
                "出发日期": "2025-08-19",
                "airport": "",
                "questions": []
            },
            "userHistory": [
                {"role": "user", "content": "3u8005"},
                {"role": "assistant", "content": ""},
                {"role": "user", "content": "9C9980晚点了吗？"},
                {"role": "assistant", "content": ""},
                {"role": "user", "content": "今天下午北京到新疆所有的航班信息"},
                {"role": "assistant", "content": ""},
                {"role": "user", "content": "到上海机票"},
                {"role": "assistant", "content": ""},
                {"role": "user", "content": "广州到长沙机票多少钱？"},
                {"role": "assistant", "content": ""}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "广州",
                "dst": "长沙",
                "主业务类型": "机票",
                "出发日期": "TODAY"
            }
        }
    },
    {
        "name": "tmall_flights_kunming_hangzhou_feb1_date_correction",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "返回日期": "",
                    "航班/车次号": "",
                    "dst": "杭州",
                    "org": "昆明",
                    "questions": [],
                    "出发日期": "2024-02-01",
                    "itinerary_num": 1,
                    "intent": "flights",
                    "主业务类型": "机票",
                    "airport": ""
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [
                {"role": "user", "content": "2月1日昆明到杭州的最早航班"}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "昆明",
                "dst": "杭州",
                "出发日期": "2026-02-01",
                "主业务类型": "机票"
            }
        }
    },
    {
        "name": "tmall_flights_shanghai_departure_only",
        "request": {
            "gptcontent": {
                "intent": "flights",
                "parameters": {
                    "arrival_time": "",
                    "dst": "",
                    "org": "上海",
                    "questions": [],
                    "cabin": "",
                    "itinerary_num": 1,
                    "sort": "departure_time_asc",
                    "intent": "flights",
                    "airport": "",
                    "返回日期": "",
                    "max_price": 0,
                    "航班/车次号": "",
                    "min_price": 0,
                    "plane_type": "",
                    "出发日期": "2023-10-01",
                    "airline": "",
                    "departure_time": "",
                    "主业务类型": "机票"
                }
            },
            "slot": {"intent": None, "slots": {}},
            "userHistory": [
                {"role": "user", "content": "上海的航班有哪些"}
            ],
            "domain": "tmall"
        },
        "expected": {
            "gptcontent": {
                "intent": "flights",
                "org": "上海",
                "dst": "",
                "出发日期": "TODAY",
                "主业务类型": "机票"
            }
        }
    },
]


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--verbose", action="store_true", help="打印详细入参与出参")
    args = parser.parse_args()

    service = IntentFixService()

    total = len(CASES)
    passed = 0
    results: List[Dict[str, Any]] = []

    for case in CASES:
        name = case["name"]
        req = case["request"]
        expected = case["expected"]

        out = service.fix_intent(
            gptcontent=req["gptcontent"],
            slot=req["slot"],
            userHistory=req["userHistory"],
            domain=req["domain"],
        )

        ok, errors = expect_subset(out, expected)
        if args.verbose:
            print(f"\n== {name} ==")
            print("request:", json.dumps(req, ensure_ascii=False))
            print("output:", json.dumps(out, ensure_ascii=False))
            print("expected_subset:", json.dumps(expected, ensure_ascii=False))

        results.append({"name": name, "pass": ok, "errors": errors, "output": out})
        if ok:
            passed += 1
        else:
            print(f"[FAIL] {name}: {errors}")

    print(f"\nSummary: {passed}/{total} passed")

    with open("validation_results.json", "w", encoding="utf-8") as f:
        json.dump({"timestamp": datetime.now().isoformat(), "results": results}, f, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    main()


