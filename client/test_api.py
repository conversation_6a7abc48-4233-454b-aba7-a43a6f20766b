#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IntentFix API 客户端测试脚本
测试Web API服务的各种功能
"""

import requests
import json
import time
from typing import Dict, Any

# API基础URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过")
            print(f"   状态: {data['status']}")
            print(f"   版本: {data['version']}")
            print(f"   运行时间: {data['uptime']}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_get_config():
    """测试获取配置"""
    print("\n⚙️  测试获取配置...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/config")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取配置成功")
            print(f"   项目名称: {data['data']['project_name']}")
            print(f"   版本: {data['data']['version']}")
            print(f"   支持的域: {data['data']['supported_domains']}")
            return True
        else:
            print(f"❌ 获取配置失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取配置异常: {e}")
        return False

def test_get_domains():
    """测试获取支持的域"""
    print("\n🌐 测试获取支持的域...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/domains")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取域列表成功")
            print(f"   支持的域: {data['data']['domains']}")
            print(f"   域数量: {data['data']['count']}")
            return True
        else:
            print(f"❌ 获取域列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取域列表异常: {e}")
        return False

def test_fix_intent_flight():
    """测试航班域意图修正"""
    print("\n✈️  测试航班域意图修正...")
    
    # 测试数据
    test_data = {
        "gptcontent": {
            "intent": "查询航班",
            "flightNo": "CA1234"
        },
        "slot": {
            "intent": "查询航班",
            "flightNo": "CA1234",
            "departCity": "北京",
            "arriveCity": "上海"
        },
        "userHistory": [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "CA1234航班什么时候起飞？"
            }
        ],
        "domain": "flight"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/fix-intent",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 航班域意图修正成功")
            print(f"   最终意图: {data['data']['gptcontent']['intent']}")
            print(f"   航班号: {data['data']['gptcontent'].get('flightNo', 'N/A')}")
            return True
        else:
            print(f"❌ 航班域意图修正失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 航班域意图修正异常: {e}")
        return False

def test_fix_intent_train():
    """测试列车域意图修正"""
    print("\n🚄 测试列车域意图修正...")
    
    # 测试数据
    test_data = {
        "gptcontent": {
            "intent": "查询列车",
            "trainNo": "G1234"
        },
        "slot": {
            "intent": "查询列车",
            "trainNo": "G1234",
            "departCity": "北京",
            "arriveCity": "上海"
        },
        "userHistory": [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "G1234列车什么时候发车？"
            }
        ],
        "domain": "train"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/fix-intent",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 列车域意图修正成功")
            print(f"   最终意图: {data['data']['gptcontent']['intent']}")
            print(f"   列车号: {data['data']['gptcontent'].get('trainNo', 'N/A')}")
            return True
        else:
            print(f"❌ 列车域意图修正失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 列车域意图修正异常: {e}")
        return False

def test_batch_fix_intent():
    """测试批量意图修正"""
    print("\n📦 测试批量意图修正...")
    
    # 批量测试数据
    batch_data = [
        {
            "gptcontent": {"intent": "查询航班", "flightNo": "CA1234"},
            "slot": {"intent": "查询航班", "flightNo": "CA1234"},
            "userHistory": [{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "CA1234延误了吗？"}],
            "domain": "flight"
        },
        {
            "gptcontent": {"intent": "预订航班"},
            "slot": {"intent": "预订航班", "departCity": "北京", "arriveCity": "上海"},
            "userHistory": [{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "帮我订一张从北京到上海的机票"}],
            "domain": "flight"
        },
        {
            "gptcontent": {"intent": "查询列车", "trainNo": "G1234"},
            "slot": {"intent": "查询列车", "trainNo": "G1234"},
            "userHistory": [{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "G1234列车什么时候发车？"}],
            "domain": "train"
        }
    ]
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/batch-fix",
            json=batch_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 批量意图修正成功")
            print(f"   总数: {data['data']['total']}")
            print(f"   成功: {data['data']['success_count']}")
            print(f"   失败: {data['data']['error_count']}")
            return True
        else:
            print(f"❌ 批量意图修正失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 批量意图修正异常: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n⚠️  测试错误处理...")
    
    # 测试无效数据
    invalid_data = {
        "gptcontent": {},
        "slot": {},
        "userHistory": [],
        "domain": "invalid_domain"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/fix-intent",
            json=invalid_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 错误处理正常")
            print(f"   返回意图: {data['data']['gptcontent']['intent']}")
            return True
        else:
            print(f"❌ 错误处理异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 错误处理异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 IntentFix API 客户端测试")
    print("=" * 50)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 执行测试
    tests = [
        test_health_check,
        test_get_config,
        test_get_domains,
        test_fix_intent_flight,
        test_fix_intent_train,
        test_batch_fix_intent,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查服务状态")

if __name__ == "__main__":
    main() 