#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IntentFix 演示脚本
展示各种使用场景和功能
"""

import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import IntentFixService


def demo_flight_scenarios():
    """演示航班域的各种场景"""
    print("=== 航班域演示 ===")
    
    service = IntentFixService()
    
    # 场景1：航班延误查询
    print("\n1. 航班延误查询")
    result1 = service.fix_intent(
        gptcontent={"intent": "查询航班", "flightNo": "CA1234"},
        slot={"intent": "查询航班", "flightNo": "CA1234"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "CA1234延误了吗？"}],
        domain="flight"
    )
    print(f"输入: CA1234延误了吗？")
    print(f"输出: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 场景2：航班预订
    print("\n2. 航班预订")
    result2 = service.fix_intent(
        gptcontent={"intent": "预订航班"},
        slot={"intent": "预订航班", "departCity": "北京", "arriveCity": "上海"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "帮我订一张从北京到上海的机票"}],
        domain="flight"
    )
    print(f"输入: 帮我订一张从北京到上海的机票")
    print(f"输出: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 场景3：改签
    print("\n3. 航班改签")
    result3 = service.fix_intent(
        gptcontent={"intent": "查询航班"},
        slot={"intent": "查询航班", "flightNo": "CA1234"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "我要改签CA1234"}],
        domain="flight"
    )
    print(f"输入: 我要改签CA1234")
    print(f"输出: {json.dumps(result3, ensure_ascii=False, indent=2)}")
    
    # 场景4：参数冲突
    print("\n4. 参数冲突处理")
    result4 = service.fix_intent(
        gptcontent={"intent": "查询航班", "departCity": "上海"},
        slot={"intent": "查询航班", "departCity": "上海", "arriveCity": "北京"},
        userHistory=[
            {"role": "user", "time": "2025-01-15T10:25:00", "type": "text", "content": "查询从北京到上海的航班"},
            {"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "上海天气怎么样？"}
        ],
        domain="flight"
    )
    print(f"输入: 上海天气怎么样？（之前说从北京出发）")
    print(f"输出: {json.dumps(result4, ensure_ascii=False, indent=2)}")


def demo_train_scenarios():
    """演示列车域的各种场景"""
    print("\n=== 列车域演示 ===")
    
    service = IntentFixService()
    
    # 场景1：列车查询
    print("\n1. 列车查询")
    result1 = service.fix_intent(
        gptcontent={"intent": "查询列车", "trainNo": "G1234"},
        slot={"intent": "查询列车", "trainNo": "G1234", "departCity": "北京", "arriveCity": "上海"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "G1234列车什么时候发车？"}],
        domain="train"
    )
    print(f"输入: G1234列车什么时候发车？")
    print(f"输出: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 场景2：列车预订
    print("\n2. 列车预订")
    result2 = service.fix_intent(
        gptcontent={"intent": "预订列车"},
        slot={"intent": "预订列车", "departCity": "北京", "arriveCity": "上海", "departDate": "2025-01-20"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "帮我订一张明天从北京到上海的高铁票"}],
        domain="train"
    )
    print(f"输入: 帮我订一张明天从北京到上海的高铁票")
    print(f"输出: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 场景3：列车延误
    print("\n3. 列车延误查询")
    result3 = service.fix_intent(
        gptcontent={"intent": "查询列车", "trainNo": "D5678"},
        slot={"intent": "查询列车", "trainNo": "D5678"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "D5678延误了吗？"}],
        domain="train"
    )
    print(f"输入: D5678延误了吗？")
    print(f"输出: {json.dumps(result3, ensure_ascii=False, indent=2)}")


def demo_parameter_validation():
    """演示参数校验功能"""
    print("\n=== 参数校验演示 ===")
    
    service = IntentFixService()
    
    # 场景1：日期格式校验
    print("\n1. 日期格式校验")
    result1 = service.fix_intent(
        gptcontent={"intent": "预订航班", "departDate": "明天"},
        slot={"intent": "预订航班", "departDate": "明天", "departCity": "北京", "arriveCity": "上海"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "帮我订明天从北京到上海的机票"}],
        domain="flight"
    )
    print(f"输入: 帮我订明天从北京到上海的机票")
    print(f"输出: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 场景2：航班号格式校验
    print("\n2. 航班号格式校验")
    result2 = service.fix_intent(
        gptcontent={"intent": "查询航班", "flightNo": "ca1234"},
        slot={"intent": "查询航班", "flightNo": "ca1234"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "ca1234航班信息"}],
        domain="flight"
    )
    print(f"输入: ca1234航班信息")
    print(f"输出: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 场景3：城市名称校验
    print("\n3. 城市名称校验")
    result3 = service.fix_intent(
        gptcontent={"intent": "预订航班", "departCity": "北京", "arriveCity": "上海"},
        slot={"intent": "预订航班", "departCity": "北京", "arriveCity": "上海"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "订北京到上海的机票"}],
        domain="flight"
    )
    print(f"输入: 订北京到上海的机票")
    print(f"output: {json.dumps(result3, ensure_ascii=False, indent=2)}")


def demo_clarification():
    """演示澄清确认功能"""
    print("\n=== 澄清确认演示 ===")
    
    service = IntentFixService()
    
    # 场景：意图不明确
    print("\n1. 意图不明确的情况")
    result = service.fix_intent(
        gptcontent={"intent": "查询航班"},
        slot={"intent": "查询航班"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "CA1234"}],
        domain="flight"
    )
    print(f"输入: CA1234")
    print(f"输出: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 场景：缺少必要参数
    print("\n2. 缺少必要参数")
    result2 = service.fix_intent(
        gptcontent={"intent": "延误登机"},
        slot={"intent": "延误登机"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "航班延误了"}],
        domain="flight"
    )
    print(f"输入: 航班延误了")
    print(f"输出: {json.dumps(result2, ensure_ascii=False, indent=2)}")


def demo_custom_domain():
    """演示自定义域"""
    print("\n=== 自定义域演示 ===")
    
    from core.intent_fix import IntentFixEngine
    
    # 创建自定义域引擎
    class HotelDomainEngine(IntentFixEngine):
        def __init__(self):
            super().__init__(domain="hotel")
            self._setup_hotel_rules()
        
        def _setup_hotel_rules(self):
            hotel_keyword_rules = {
                "预订酒店": ["预订", "订房", "开房", "住宿", "酒店"],
                "查询酒店": ["查询", "查找", "搜索", "酒店信息"],
                "取消预订": ["取消", "退订", "不要了"]
            }
            self.add_custom_rules(domain="hotel", keyword_rules=hotel_keyword_rules)
    
    # 注册自定义域
    service = IntentFixService()
    service.add_custom_domain("hotel", HotelDomainEngine())
    
    # 测试自定义域
    result = service.fix_intent(
        gptcontent={"intent": "查询酒店"},
        slot={"intent": "查询酒店", "city": "北京"},
        userHistory=[{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "查询北京的酒店"}],
        domain="hotel"
    )
    print(f"输入: 查询北京的酒店")
    print(f"输出: {json.dumps(result, ensure_ascii=False, indent=2)}")


def main():
    """主函数"""
    print("IntentFix 意图修正模块演示")
    print("=" * 50)
    
    try:
        # 演示各种场景
        demo_flight_scenarios()
        demo_train_scenarios()
        demo_parameter_validation()
        demo_clarification()
        demo_custom_domain()
        
        print("\n" + "=" * 50)
        print("演示完成！")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 