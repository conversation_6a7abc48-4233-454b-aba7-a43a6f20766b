from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime


class UserMessage(BaseModel):
    """用户消息模型"""
    role: str = Field(..., description="消息角色：user/system")
    time: datetime = Field(..., description="消息时间")
    type: str = Field(..., description="消息类型")
    content: str = Field(..., description="消息内容")


class SlotInfo(BaseModel):
    """槽位信息模型"""
    intent: Optional[str] = Field(None, description="意图")
    
    # tmall domain格式字段
    出发日期: Optional[str] = Field(None, description="出发日期")
    返回日期: Optional[str] = Field(None, description="返回日期")
    主业务类型: Optional[str] = Field(None, description="主业务类型")
    航班车次号: Optional[str] = Field(None, description="航班/车次号")
    org: Optional[str] = Field(None, description="出发地")
    dst: Optional[str] = Field(None, description="目的地")
    
    # 兼容字段（用于其他domain）
    departDate: Optional[str] = Field(None, description="出发日期")
    mainBusinessType: Optional[str] = Field(None, description="主业务类型")
    
    class Config:
        extra = "allow"  # 允许额外的字段


class GptContent(BaseModel):
    """GPT内容模型 - 根据实际使用结构更新"""
    intent: str = Field(..., description="意图")
    
    # tmall domain格式字段
    返回日期: Optional[str] = Field("", description="返回日期")
    航班车次号: Optional[str] = Field("", description="航班/车次号")
    dst: Optional[str] = Field("", description="目的地")
    org: Optional[str] = Field("", description="出发地")
    questions: Optional[List[str]] = Field(default_factory=list, description="问题列表")
    出发日期: Optional[str] = Field("", description="出发日期")
    itinerary_num: Optional[str] = Field("", description="行程编号")
    主业务类型: Optional[str] = Field("", description="主业务类型")
    airport: Optional[str] = Field("", description="机场信息")
    departure_time: Optional[str] = Field("", description="出发时间段")
    airline: Optional[str] = Field("", description="航空公司代码")
    
    class Config:
        extra = "allow"  # 允许额外的字段


class IntentFixInput(BaseModel):
    """意图修正输入模型"""
    gptcontent: GptContent = Field(..., description="大模型输出的初步意图和槽位参数")
    slot: SlotInfo = Field(..., description="Tripnow提取的槽位和意图信息")
    userHistory: List[UserMessage] = Field(..., description="用户与系统的历史对话")
    domain: str = Field(..., description="业务域")


class IntentFixOutput(BaseModel):
    """意图修正输出模型"""
    gptcontent: GptContent = Field(..., description="修正后的意图和槽位参数")
    resultReply: Optional[str] = Field(None, description="需要澄清确认时的回复内容")


class IntentCandidate(BaseModel):
    """意图候选模型"""
    intent: str = Field(..., description="意图名称")
    confidence: float = Field(..., description="置信度")
    source: str = Field(..., description="来源：rule/rasa/model")


class RuleResult(BaseModel):
    """规则校验结果模型"""
    intent: Optional[str] = Field(None, description="规则识别的意图")
    confidence: float = Field(0.0, description="置信度")
    matched_keywords: List[str] = Field(default_factory=list, description="匹配的关键词")
    matched_patterns: List[str] = Field(default_factory=list, description="匹配的正则模式")
    extracted_number: Optional[str] = Field(None, description="提取的航班/车次号")


class ParameterConflict(BaseModel):
    """参数冲突模型"""
    slot_name: str = Field(..., description="冲突的槽位名称")
    current_value: Any = Field(..., description="当前值")
    history_value: Any = Field(..., description="历史值")
    conflict_type: str = Field(..., description="冲突类型：explicit/implicit")
    resolution: Optional[str] = Field(None, description="解决方案") 