import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dateutil import parser
from core.models import ParameterConflict, SlotInfo, GptContent, UserMessage


class ParameterValidator:
    """参数校验修正模块"""
    
    def __init__(self):
        # 时间格式正则表达式
        self.date_patterns = [
            r'\d{4}-\d{2}-\d{2}',  # 2025-01-01
            r'\d{2}-\d{2}',         # 01-01
            r'\d{4}/\d{2}/\d{2}',   # 2025/01/01
            r'\d{2}/\d{2}',         # 01/01
        ]
        
        # 航班号正则表达式 - 支持多种格式
        self.flight_patterns = [
            r'[A-Z]{2}\d{3,4}',      # 标准格式：CA1234, MU5678
            r'\d[A-Z]\d{3,4}',       # 数字开头：9C9980, 3U8925
            r'[A-Z]\d[A-Z]\d{3,4}',  # 字母数字字母：如果有这种格式
        ]
        
        # 列车号正则表达式（根据Java规则更新）
        self.train_pattern = r'[GDTCKZXYL]\d{1,4}'
        
        # 城市名称映射（全国主要城市）
        self.city_mapping = {
            # 直辖市
            "北京": "北京",
            "上海": "上海",
            "天津": "天津",
            "重庆": "重庆",
            
            # 省会城市
            "广州": "广州",  # 广东
            "深圳": "深圳",  # 广东（计划单列市）
            "杭州": "杭州",  # 浙江
            "南京": "南京",  # 江苏
            "成都": "成都",  # 四川
            "武汉": "武汉",  # 湖北
            "西安": "西安",  # 陕西
            "郑州": "郑州",  # 河南
            "长春": "长春",  # 吉林
            "沈阳": "沈阳",  # 辽宁
            "哈尔滨": "哈尔滨",  # 黑龙江
            "石家庄": "石家庄",  # 河北
            "太原": "太原",  # 山西
            "呼和浩特": "呼和浩特",  # 内蒙古
            "长沙": "长沙",  # 湖南
            "南昌": "南昌",  # 江西
            "合肥": "合肥",  # 安徽
            "福州": "福州",  # 福建
            "南宁": "南宁",  # 广西
            "海口": "海口",  # 海南
            "昆明": "昆明",  # 云南
            "贵阳": "贵阳",  # 贵州
            "拉萨": "拉萨",  # 西藏
            "兰州": "兰州",  # 甘肃
            "西宁": "西宁",  # 青海
            "银川": "银川",  # 宁夏
            "乌鲁木齐": "乌鲁木齐",  # 新疆
            "济南": "济南",  # 山东
            
            # 计划单列市
            "大连": "大连",  # 辽宁
            "青岛": "青岛",  # 山东
            "宁波": "宁波",  # 浙江
            "厦门": "厦门",  # 福建
            
            # 重要地级市
            "苏州": "苏州",  # 江苏
            "无锡": "无锡",  # 江苏
            "常州": "常州",  # 江苏
            "南通": "南通",  # 江苏
            "徐州": "徐州",  # 江苏
            "连云港": "连云港",  # 江苏
            "淮安": "淮安",  # 江苏
            "盐城": "盐城",  # 江苏
            "扬州": "扬州",  # 江苏
            "镇江": "镇江",  # 江苏
            "泰州": "泰州",  # 江苏
            "宿迁": "宿迁",  # 江苏
            "新沂": "新沂",  # 江苏
            
            "温州": "温州",  # 浙江
            "嘉兴": "嘉兴",  # 浙江
            "湖州": "湖州",  # 浙江
            "绍兴": "绍兴",  # 浙江
            "金华": "金华",  # 浙江
            "衢州": "衢州",  # 浙江
            "舟山": "舟山",  # 浙江
            "台州": "台州",  # 浙江
            "丽水": "丽水",  # 浙江
            
            "佛山": "佛山",  # 广东
            "东莞": "东莞",  # 广东
            "中山": "中山",  # 广东
            "珠海": "珠海",  # 广东
            "汕头": "汕头",  # 广东
            "韶关": "韶关",  # 广东
            "惠州": "惠州",  # 广东
            "江门": "江门",  # 广东
            "湛江": "湛江",  # 广东
            "茂名": "茂名",  # 广东
            "肇庆": "肇庆",  # 广东
            "梅州": "梅州",  # 广东
            "汕尾": "汕尾",  # 广东
            "河源": "河源",  # 广东
            "阳江": "阳江",  # 广东
            "清远": "清远",  # 广东
            "潮州": "潮州",  # 广东
            "揭阳": "揭阳",  # 广东
            "云浮": "云浮",  # 广东
            
            "烟台": "烟台",  # 山东
            "潍坊": "潍坊",  # 山东
            "临沂": "临沂",  # 山东
            "淄博": "淄博",  # 山东
            "济宁": "济宁",  # 山东
            "泰安": "泰安",  # 山东
            "威海": "威海",  # 山东
            "日照": "日照",  # 山东
            "滨州": "滨州",  # 山东
            "德州": "德州",  # 山东
            "聊城": "聊城",  # 山东
            "菏泽": "菏泽",  # 山东
            "枣庄": "枣庄",  # 山东
            "东营": "东营",  # 山东
            "莱芜": "莱芜",  # 山东
            
            "洛阳": "洛阳",  # 河南
            "开封": "开封",  # 河南
            "平顶山": "平顶山",  # 河南
            "安阳": "安阳",  # 河南
            "鹤壁": "鹤壁",  # 河南
            "新乡": "新乡",  # 河南
            "焦作": "焦作",  # 河南
            "濮阳": "濮阳",  # 河南
            "许昌": "许昌",  # 河南
            "漯河": "漯河",  # 河南
            "三门峡": "三门峡",  # 河南
            "南阳": "南阳",  # 河南
            "商丘": "商丘",  # 河南
            "信阳": "信阳",  # 河南
            "周口": "周口",  # 河南
            "驻马店": "驻马店",  # 河南
            
            "唐山": "唐山",  # 河北
            "秦皇岛": "秦皇岛",  # 河北
            "邯郸": "邯郸",  # 河北
            "邢台": "邢台",  # 河北
            "保定": "保定",  # 河北
            "张家口": "张家口",  # 河北
            "承德": "承德",  # 河北
            "沧州": "沧州",  # 河北
            "廊坊": "廊坊",  # 河北
            "衡水": "衡水",  # 河北
            
            "宜昌": "宜昌",  # 湖北
            "襄阳": "襄阳",  # 湖北
            "荆州": "荆州",  # 湖北
            "十堰": "十堰",  # 湖北
            "黄石": "黄石",  # 湖北
            "鄂州": "鄂州",  # 湖北
            "荆门": "荆门",  # 湖北
            "孝感": "孝感",  # 湖北
            "黄冈": "黄冈",  # 湖北
            "咸宁": "咸宁",  # 湖北
            "随州": "随州",  # 湖北
            
            "株洲": "株洲",  # 湖南
            "湘潭": "湘潭",  # 湖南
            "衡阳": "衡阳",  # 湖南
            "邵阳": "邵阳",  # 湖南
            "岳阳": "岳阳",  # 湖南
            "常德": "常德",  # 湖南
            "张家界": "张家界",  # 湖南
            "益阳": "益阳",  # 湖南
            "郴州": "郴州",  # 湖南
            "永州": "永州",  # 湖南
            "怀化": "怀化",  # 湖南
            "娄底": "娄底",  # 湖南
            
            "绵阳": "绵阳",  # 四川
            "德阳": "德阳",  # 四川
            "南充": "南充",  # 四川
            "宜宾": "宜宾",  # 四川
            "自贡": "自贡",  # 四川
            "乐山": "乐山",  # 四川
            "泸州": "泸州",  # 四川
            "达州": "达州",  # 四川
            "内江": "内江",  # 四川
            "遂宁": "遂宁",  # 四川
            "攀枝花": "攀枝花",  # 四川
            "眉山": "眉山",  # 四川
            "广安": "广安",  # 四川
            "资阳": "资阳",  # 四川
            "凉山": "凉山",  # 四川
            "甘孜": "甘孜",  # 四川
            "阿坝": "阿坝",  # 四川
            
            "宝鸡": "宝鸡",  # 陕西
            "咸阳": "咸阳",  # 陕西
            "渭南": "渭南",  # 陕西
            "延安": "延安",  # 陕西
            "汉中": "汉中",  # 陕西
            "榆林": "榆林",  # 陕西
            "安康": "安康",  # 陕西
            "商洛": "商洛",  # 陕西
            
            "大同": "大同",  # 山西
            "阳泉": "阳泉",  # 山西
            "长治": "长治",  # 山西
            "晋城": "晋城",  # 山西
            "朔州": "朔州",  # 山西
            "晋中": "晋中",  # 山西
            "运城": "运城",  # 山西
            "忻州": "忻州",  # 山西
            "临汾": "临汾",  # 山西
            "吕梁": "吕梁",  # 山西
            
            "包头": "包头",  # 内蒙古
            "乌海": "乌海",  # 内蒙古
            "赤峰": "赤峰",  # 内蒙古
            "通辽": "通辽",  # 内蒙古
            "鄂尔多斯": "鄂尔多斯",  # 内蒙古
            "呼伦贝尔": "呼伦贝尔",  # 内蒙古
            "巴彦淖尔": "巴彦淖尔",  # 内蒙古
            "乌兰察布": "乌兰察布",  # 内蒙古
            
            "鞍山": "鞍山",  # 辽宁
            "抚顺": "抚顺",  # 辽宁
            "本溪": "本溪",  # 辽宁
            "丹东": "丹东",  # 辽宁
            "锦州": "锦州",  # 辽宁
            "营口": "营口",  # 辽宁
            "阜新": "阜新",  # 辽宁
            "辽阳": "辽阳",  # 辽宁
            "盘锦": "盘锦",  # 辽宁
            "铁岭": "铁岭",  # 辽宁
            "朝阳": "朝阳",  # 辽宁
            "葫芦岛": "葫芦岛",  # 辽宁
            
            "吉林": "吉林",  # 吉林
            "四平": "四平",  # 吉林
            "辽源": "辽源",  # 吉林
            "通化": "通化",  # 吉林
            "白山": "白山",  # 吉林
            "松原": "松原",  # 吉林
            "白城": "白城",  # 吉林
            "延边": "延边",  # 吉林
            
            "齐齐哈尔": "齐齐哈尔",  # 黑龙江
            "鸡西": "鸡西",  # 黑龙江
            "鹤岗": "鹤岗",  # 黑龙江
            "双鸭山": "双鸭山",  # 黑龙江
            "大庆": "大庆",  # 黑龙江
            "伊春": "伊春",  # 黑龙江
            "佳木斯": "佳木斯",  # 黑龙江
            "七台河": "七台河",  # 黑龙江
            "牡丹江": "牡丹江",  # 黑龙江
            "黑河": "黑河",  # 黑龙江
            "绥化": "绥化",  # 黑龙江
            
            "芜湖": "芜湖",  # 安徽
            "蚌埠": "蚌埠",  # 安徽
            "淮南": "淮南",  # 安徽
            "马鞍山": "马鞍山",  # 安徽
            "淮北": "淮北",  # 安徽
            "铜陵": "铜陵",  # 安徽
            "安庆": "安庆",  # 安徽
            "黄山": "黄山",  # 安徽
            "滁州": "滁州",  # 安徽
            "阜阳": "阜阳",  # 安徽
            "宿州": "宿州",  # 安徽
            "六安": "六安",  # 安徽
            "亳州": "亳州",  # 安徽
            "池州": "池州",  # 安徽
            "宣城": "宣城",  # 安徽
            
            "莆田": "莆田",  # 福建
            "三明": "三明",  # 福建
            "泉州": "泉州",  # 福建
            "漳州": "漳州",  # 福建
            "南平": "南平",  # 福建
            "龙岩": "龙岩",  # 福建
            "宁德": "宁德",  # 福建
            
            "赣州": "赣州",  # 江西
            "景德镇": "景德镇",  # 江西
            "萍乡": "萍乡",  # 江西
            "九江": "九江",  # 江西
            "新余": "新余",  # 江西
            "鹰潭": "鹰潭",  # 江西
            "吉安": "吉安",  # 江西
            "宜春": "宜春",  # 江西
            "抚州": "抚州",  # 江西
            "上饶": "上饶",  # 江西
            
            "柳州": "柳州",  # 广西
            "桂林": "桂林",  # 广西
            "梧州": "梧州",  # 广西
            "北海": "北海",  # 广西
            "防城港": "防城港",  # 广西
            "钦州": "钦州",  # 广西
            "贵港": "贵港",  # 广西
            "玉林": "玉林",  # 广西
            "百色": "百色",  # 广西
            "贺州": "贺州",  # 广西
            "河池": "河池",  # 广西
            "来宾": "来宾",  # 广西
            "崇左": "崇左",  # 广西
            
            "三亚": "三亚",  # 海南
            "三沙": "三沙",  # 海南
            "儋州": "儋州",  # 海南
            
            "曲靖": "曲靖",  # 云南
            "玉溪": "玉溪",  # 云南
            "保山": "保山",  # 云南
            "昭通": "昭通",  # 云南
            "丽江": "丽江",  # 云南
            "普洱": "普洱",  # 云南
            "临沧": "临沧",  # 云南
            "楚雄": "楚雄",  # 云南
            "红河": "红河",  # 云南
            "文山": "文山",  # 云南
            "西双版纳": "西双版纳",  # 云南
            "大理": "大理",  # 云南
            "德宏": "德宏",  # 云南
            "怒江": "怒江",  # 云南
            "迪庆": "迪庆",  # 云南
            
            "六盘水": "六盘水",  # 贵州
            "遵义": "遵义",  # 贵州
            "安顺": "安顺",  # 贵州
            "毕节": "毕节",  # 贵州
            "铜仁": "铜仁",  # 贵州
            "黔西南": "黔西南",  # 贵州
            "黔东南": "黔东南",  # 贵州
            "黔南": "黔南",  # 贵州
            
            "嘉峪关": "嘉峪关",  # 甘肃
            "金昌": "金昌",  # 甘肃
            "白银": "白银",  # 甘肃
            "天水": "天水",  # 甘肃
            "武威": "武威",  # 甘肃
            "张掖": "张掖",  # 甘肃
            "平凉": "平凉",  # 甘肃
            "酒泉": "酒泉",  # 甘肃
            "庆阳": "庆阳",  # 甘肃
            "定西": "定西",  # 甘肃
            "陇南": "陇南",  # 甘肃
            
            "石嘴山": "石嘴山",  # 宁夏
            "吴忠": "吴忠",  # 宁夏
            "固原": "固原",  # 宁夏
            "中卫": "中卫",  # 宁夏
            
            "克拉玛依": "克拉玛依",  # 新疆
            "吐鲁番": "吐鲁番",  # 新疆
            "哈密": "哈密",  # 新疆
            "昌吉": "昌吉",  # 新疆
            "博尔塔拉": "博尔塔拉",  # 新疆
            "巴音郭楞": "巴音郭楞",  # 新疆
            "阿克苏": "阿克苏",  # 新疆
            "克孜勒苏": "克孜勒苏",  # 新疆
            "喀什": "喀什",  # 新疆
            "和田": "和田",  # 新疆
            "伊犁": "伊犁",  # 新疆
            "塔城": "塔城",  # 新疆
            "阿勒泰": "阿勒泰",  # 新疆
            "库车": "库车",  # 新疆
            
            # 港澳台
            "香港": "香港",
            "澳门": "澳门",
            "台北": "台北",
            "高雄": "高雄",
            "台中": "台中",
            "台南": "台南",
            
            # 国际城市（常见目的地）
            "多哈": "多哈",  # 卡塔尔
            "迪拜": "迪拜",  # 阿联酋
            "新加坡": "新加坡",
            "吉隆坡": "吉隆坡",  # 马来西亚
            "曼谷": "曼谷",  # 泰国
            "东京": "东京",  # 日本
            "大阪": "大阪",  # 日本
            "首尔": "首尔",  # 韩国
            "釜山": "釜山",  # 韩国
            "纽约": "纽约",  # 美国
            "洛杉矶": "洛杉矶",  # 美国
            "旧金山": "旧金山",  # 美国
            "伦敦": "伦敦",  # 英国
            "巴黎": "巴黎",  # 法国
            "法兰克福": "法兰克福",  # 德国
            "阿姆斯特丹": "阿姆斯特丹",  # 荷兰
            "悉尼": "悉尼",  # 澳大利亚
            "墨尔本": "墨尔本",  # 澳大利亚
            "温哥华": "温哥华",  # 加拿大
            "多伦多": "多伦多",  # 加拿大
            
            # 常见别名映射
            "广东": "广州",  # 广东省会
            "浙江": "杭州",  # 浙江省会
            "江苏": "南京",  # 江苏省会
            "四川": "成都",  # 四川省会
            "湖北": "武汉",  # 湖北省会
            "陕西": "西安",  # 陕西省会
            "河南": "郑州",  # 河南省会
            "山东": "济南",  # 山东省会
            "湖南": "长沙",  # 湖南省会
            "安徽": "合肥",  # 安徽省会
            "福建": "福州",  # 福建省会
            "江西": "南昌",  # 江西省会
            "河北": "石家庄",  # 河北省会
            "山西": "太原",  # 山西省会
            "辽宁": "沈阳",  # 辽宁省会
            "吉林": "长春",  # 吉林省会
            "黑龙江": "哈尔滨",  # 黑龙江省会
            "内蒙古": "呼和浩特",  # 内蒙古首府
            "广西": "南宁",  # 广西首府
            "海南": "海口",  # 海南省会
            "云南": "昆明",  # 云南省会
            "贵州": "贵阳",  # 贵州省会
            "西藏": "拉萨",  # 西藏首府
            "甘肃": "兰州",  # 甘肃省会
            "青海": "西宁",  # 青海省会
            "宁夏": "银川",  # 宁夏首府
            "新疆": "乌鲁木齐",  # 新疆首府
        }
        
        # 时间关键词映射
        self.time_keywords = {
            "今天": 0,
            "明天": 1,
            "后天": 2,
            "大后天": 3,
            "昨天": -1,
            "前天": -2
        }
        
        # 时间段关键词映射
        self.time_period_keywords = {
            "早上": "06:00-12:00",
            "上午": "06:00-12:00", 
            "中午": "11:00-14:00",
            "下午": "12:00-18:00",
            "晚上": "18:00-23:00",
            "夜里": "23:00-06:00",
            "凌晨": "00:00-06:00"
        }
        # 星期关键词映射（周几/星期几 -> 最近的该星期（含今天））
        self.weekday_keywords = {
            "星期一": 0, "周一": 0,
            "星期二": 1, "周二": 1,
            "星期三": 2, "周三": 2,
            "星期四": 3, "周四": 3,
            "星期五": 4, "周五": 4,
            "星期六": 5, "周六": 5,
            "星期天": 6, "星期日": 6, "周日": 6
        }
    
    def validate_date_format(self, date_str: str, from_user_query: bool = False) -> Optional[str]:
        """校验日期格式
        
        Args:
            date_str: 日期字符串
            from_user_query: 是否来自用户查询提取，如果是则对过去日期加一年
        """
        if not date_str:
            return None
        
        # 尝试解析日期
        try:
            # 处理相对日期
            if date_str in self.time_keywords:
                days_offset = self.time_keywords[date_str]
                target_date = datetime.now() + timedelta(days=days_offset)
                # 若早于今天，则矫正为今天
                if target_date.date() < datetime.now().date():
                    target_date = datetime.now()
                return target_date.strftime("%Y-%m-%d")
            # 处理星期几/周几（取最近的该星期，若今天即符合则取今天）
            if date_str in self.weekday_keywords:
                target_wd = self.weekday_keywords[date_str]
                today_wd = datetime.now().weekday()
                delta = (target_wd - today_wd) % 7
                target_date = datetime.now() + timedelta(days=delta)
                # 正常情况下不会小于今天，这里保持一致处理
                if target_date.date() < datetime.now().date():
                    target_date = datetime.now()
                return target_date.strftime("%Y-%m-%d")
            
            # 处理绝对日期
            for pattern in self.date_patterns:
                if re.match(pattern, date_str):
                    # 如果是短格式，添加当前年份
                    if re.match(r'\d{2}-\d{2}', date_str):
                        date_str = f"{datetime.now().year}-{date_str}"
                    elif re.match(r'\d{2}/\d{2}', date_str):
                        date_str = f"{datetime.now().year}/{date_str}"
                    
                    parsed_date = parser.parse(date_str)
                    # 只有从用户查询提取的过去日期才加一年
                    if from_user_query and parsed_date.date() < datetime.now().date():
                        parsed_date = parsed_date.replace(year=parsed_date.year + 1)
                    return parsed_date.strftime("%Y-%m-%d")
            
            return None
        except:
            return None
    
    def validate_flight_number(self, flight_no: str) -> Optional[str]:
        """校验航班号格式"""
        if not flight_no:
            return None
        
        # 尝试所有航班号格式
        for pattern in self.flight_patterns:
            if re.match(pattern, flight_no.upper()):
                return flight_no.upper()
        return None
    
    def validate_train_number(self, train_no: str) -> Optional[str]:
        """校验列车号格式"""
        if not train_no:
            return None
        
        if re.match(self.train_pattern, train_no.upper()):
            return train_no.upper()
        return None
    
    def validate_city_name(self, city: str) -> Optional[str]:
        """校验城市名称"""
        if not city:
            return None
        
        # 检查是否在映射中
        if city in self.city_mapping:
            return self.city_mapping[city]
        
        # 模糊匹配
        for mapped_city in self.city_mapping.values():
            if city in mapped_city or mapped_city in city:
                return mapped_city
        
        return city
    
    def validate_slot_value(self, slot_name: str, value: Any) -> Tuple[Any, bool]:
        """校验槽位值"""
        if not value:
            return value, True
        
        original_value = value
        is_valid = True
        
        if slot_name == "departDate":
            validated_value = self.validate_date_format(str(value), from_user_query=False)
            if validated_value:
                value = validated_value
            else:
                is_valid = False
        
        return value, is_valid
    
    def check_parameter_conflicts(self, current_slot: SlotInfo, history_slots: List[SlotInfo]) -> List[ParameterConflict]:
        """检查参数冲突"""
        conflicts = []
        
        if not history_slots:
            return conflicts
        
        # 获取最近的槽位信息
        latest_history = history_slots[-1]
        
        # 检查每个槽位的冲突
        for slot_name in ["departDate"]:
            current_value = getattr(current_slot, slot_name, None)
            history_value = getattr(latest_history, slot_name, None)
            
            if current_value and history_value and current_value != history_value:
                # 判断冲突类型
                conflict_type = self._determine_conflict_type(current_value, history_value)
                
                conflicts.append(ParameterConflict(
                    slot_name=slot_name,
                    current_value=current_value,
                    history_value=history_value,
                    conflict_type=conflict_type
                ))
        
        return conflicts
    
    def _determine_conflict_type(self, current_value: Any, history_value: Any) -> str:
        """判断冲突类型"""
        # 这里可以根据具体的业务逻辑来判断是明确修改还是隐含修改
        # 简单实现：如果值完全不同，认为是隐含修改
        return "implicit"
    
    def resolve_conflicts(self, conflicts: List[ParameterConflict], query: str) -> Dict[str, Any]:
        """解决参数冲突"""
        resolutions = {}
        
        for conflict in conflicts:
            # 检查查询中是否有明确的修改意图
            if self._has_explicit_modification(query, conflict.slot_name):
                conflict.conflict_type = "explicit"
                resolutions[conflict.slot_name] = conflict.current_value
            else:
                # 隐含修改，需要澄清
                conflict.resolution = "need_clarification"
                resolutions[conflict.slot_name] = None
        
        return resolutions
    
    def _has_explicit_modification(self, query: str, slot_name: str) -> bool:
        """检查是否有明确的修改意图"""
        modification_keywords = {
            "departDate": ["改", "换", "修改", "变更", "调整"]
        }
        
        keywords = modification_keywords.get(slot_name, [])
        return any(keyword in query for keyword in keywords)
    
    def update_slot_values(self, slot: SlotInfo, query: str) -> SlotInfo:
        """动态更新槽位值"""
        updated_slot = slot.copy()
        
        # 检查查询中的时间关键词
        for keyword, days_offset in self.time_keywords.items():
            if keyword in query:
                target_date = datetime.now() + timedelta(days=days_offset)
                date_str = target_date.strftime("%Y-%m-%d")
                
                # 更新相关日期槽位
                if "出发" in query or "离开" in query:
                    updated_slot.departDate = date_str
                elif "到达" in query or "去" in query:
                    # 这里可能需要根据上下文判断是出发日期还是到达日期
                    pass
        
        # 检查航班号
        for pattern in self.flight_patterns:
            flight_match = re.search(pattern, query.upper())
            if flight_match:
                updated_slot.flightNo = flight_match.group()
                break
        
        # 检查列车号
        train_match = re.search(self.train_pattern, query.upper())
        if train_match:
            updated_slot.trainNo = train_match.group()
        
        # 检查城市名称
        for city in self.city_mapping.keys():
            if city in query:
                if "出发" in query or "从" in query:
                    updated_slot.departCity = self.city_mapping[city]
                elif "到达" in query or "到" in query or "去" in query:
                    updated_slot.arriveCity = self.city_mapping[city]
        
        # 若上下文无时间概念，则出发日期强制为今天
        if not self._query_has_time_concept(query):
            today_str = datetime.now().strftime("%Y-%m-%d")
            updated_slot.departDate = today_str
            try:
                setattr(updated_slot, '出发日期', today_str)
            except Exception:
                pass
        return updated_slot

    def _is_from_relative_keywords(self, text: str) -> bool:
        """检测查询是否包含相对日期关键词（昨天、前天等）"""
        if not text:
            return False
        # 检查相对日期关键词
        relative_keywords = ["昨天", "前天", "大前天"]
        return any(keyword in text for keyword in relative_keywords)
    
    def _query_has_time_concept(self, text: str) -> bool:
        """检测文本中是否包含时间相关的概念（相对日期、星期、日期/时间模式）"""
        if not text:
            return False
        # 关键词
        if any(k in text for k in self.time_keywords.keys()):
            return True
        if any(k in text for k in getattr(self, 'weekday_keywords', {}).keys()):
            return True
        # 正则
        # 轻量日期/时间模式
        patterns = [
            r"\b\d{4}-\d{2}-\d{2}\b",  # YYYY-MM-DD
            r"\b\d{4}/\d{2}/\d{2}\b",  # YYYY/MM/DD
            r"\b\d{2}-\d{2}\b",        # MM-DD
            r"\b\d{2}/\d{2}\b",        # MM/DD
            r"\d{1,2}点"                  # 8点
        ]
        for p in patterns:
            if re.search(p, text):
                return True
        return False
    
    def generate_clarification_message(self, conflicts: List[ParameterConflict]) -> str:
        """生成澄清消息"""
        if not conflicts:
            return ""
        
        messages = []
        for conflict in conflicts:
            if conflict.conflict_type == "implicit":
                slot_name_map = {
                    "departDate": "出发日期"
                }
                
                slot_display = slot_name_map.get(conflict.slot_name, conflict.slot_name)
                messages.append(
                    f"您之前说{slot_display}是{conflict.history_value}，"
                    f"现在要改为{conflict.current_value}吗？"
                )
        
        return "；".join(messages) if messages else "" 

    def validate_gpt_content(self, gpt_content: GptContent, query: str, domain: str = "tmall", user_history: Optional[List[UserMessage]] = None) -> GptContent:
        """校验GPT内容的核心字段"""
        validated_content = gpt_content.copy()
        
        if domain == "tmall":
            # tmall domain格式处理
            validated_content.org = self.validate_city_name(gpt_content.org or "")
            validated_content.dst = self.validate_city_name(gpt_content.dst or "")
            validated_content.出发日期 = self.validate_date_format(gpt_content.出发日期 or "", from_user_query=False)
            validated_content.返回日期 = self.validate_date_format(gpt_content.返回日期 or "", from_user_query=False)
            validated_content.航班车次号 = self.validate_flight_train_number(gpt_content.航班车次号 or "")
            # 处理航空公司代码字段 - 从多个来源获取
            airline_value = getattr(gpt_content, 'airline', "") or ""
            # 如果没有直接的airline字段，尝试从parameters中获取
            if not airline_value and hasattr(gpt_content, 'parameters') and isinstance(gpt_content.parameters, dict):
                airline_value = gpt_content.parameters.get('airline', "") or ""
            validated_content.airline = airline_value
            # 主业务类型：避免将整个对象错误赋值；按意图/关键词智能默认
            biz_type = (gpt_content.主业务类型 or "").strip()
            if not biz_type:
                intent_lower = (gpt_content.intent or "").lower()
                if intent_lower in ["flights", "flight"] or any(k in (query or "") for k in ["航班", "机票", "飞机"]):
                    biz_type = "机票"
                elif intent_lower in ["train", "trains"] or any(k in (query or "") for k in ["火车", "高铁", "动车", "列车", "火车票"]):
                    biz_type = "火车票"
                else:
                    biz_type = ""
            validated_content.主业务类型 = biz_type

            # 出发日期策略：
            # 1) 若当前query包含时间概念，则已在后续提取中处理
            # 2) 否则若历史有时间，则使用历史中最新出现的时间（优先于现有值）
            # 3) 否则若当前结果缺失，则默认今天
            # 4) 对于过去的绝对日期（如2023年的日期），修正为今天
            if not self._query_has_time_concept(query):
                history_date = self._extract_latest_date_from_history(user_history or [])
                if history_date:
                    # 历史中有明确的时间概念，优先使用
                    validated_content.出发日期 = history_date
                elif not validated_content.出发日期:
                    # 历史中没有时间概念，且当前也没有值，则默认今天
                    validated_content.出发日期 = datetime.now().strftime("%Y-%m-%d")
            
            # 修正过去的绝对日期为今天（但保留通过相对日期关键词生成的日期）
            if validated_content.出发日期 and not self._is_from_relative_keywords(query):
                try:
                    parsed_date = parser.parse(validated_content.出发日期)
                    today = datetime.now().date()
                    
                    # 如果是过去的日期，修正为今天
                    if parsed_date.date() < today:
                        validated_content.出发日期 = datetime.now().strftime("%Y-%m-%d")
                except:
                    pass
            
            # 从查询中提取并更新字段
            validated_content = self._extract_from_query_tmall(validated_content, query)
            
            # 处理机场字段
            validated_content = self.process_airport_field(validated_content, query)
            
            # 如果是信息查询意图，清空位置和时间字段
            if validated_content.intent == "normal":
                validated_content = self.clear_location_fields_for_info_query(validated_content)
        else:
            # 其他domain格式处理
            depart_date = gpt_content.departDate or gpt_content.出发日期 or ""
            return_date = gpt_content.returnDate or gpt_content.返回日期 or ""
            flight_train_no = gpt_content.flightTrainNo or gpt_content.航班车次号 or ""
            main_business_type = gpt_content.mainBusinessType or gpt_content.主业务类型 or ""
            
            # 校验核心字段
            validated_content.org = self.validate_city_name(gpt_content.org or "")
            validated_content.dst = self.validate_city_name(gpt_content.dst or "")
            validated_content.departDate = self.validate_date_format(depart_date, from_user_query=False)
            validated_content.returnDate = self.validate_date_format(return_date, from_user_query=False)
            validated_content.flightTrainNo = self.validate_flight_train_number(flight_train_no)
            validated_content.mainBusinessType = main_business_type
            
            # 同步兼容字段
            validated_content.出发日期 = validated_content.departDate
            validated_content.返回日期 = validated_content.returnDate
            validated_content.航班车次号 = validated_content.flightTrainNo
            validated_content.主业务类型 = validated_content.mainBusinessType
            
            # 从查询中提取并更新字段
            validated_content = self._extract_from_query(validated_content, query)
        
        return validated_content
    
    def validate_flight_train_number(self, flight_train_no: str) -> Optional[str]:
        """校验航班/车次号格式"""
        if not flight_train_no:
            return None
        
        # 先尝试作为航班号校验
        flight_validated = self.validate_flight_number(flight_train_no)
        if flight_validated:
            return flight_validated
        
        # 再尝试作为车次号校验
        train_validated = self.validate_train_number(flight_train_no)
        if train_validated:
            return train_validated
        
        return None
    
    def _extract_from_query(self, gpt_content: GptContent, query: str) -> GptContent:
        """从查询中提取信息并更新GPT内容"""
        updated_content = gpt_content.copy()
        
        # 提取出发地和目的地
        for city in self.city_mapping.keys():
            if city in query:
                has_from = ("出发" in query) or ("从" in query)
                has_to = ("到达" in query) or ("到" in query) or ("去" in query)
                if has_from:
                    updated_content.org = self.city_mapping[city]
                elif has_to:
                    updated_content.dst = self.city_mapping[city]
        
        # 提取日期信息
        # 相对日期（今天/明天/后天...）
        for keyword, days_offset in self.time_keywords.items():
            if keyword in query:
                target_date = datetime.now() + timedelta(days=days_offset)
                date_str = target_date.strftime("%Y-%m-%d")
                if "出发" in query or "离开" in query:
                    updated_content.departDate = date_str
                elif "返回" in query or "回来" in query:
                    updated_content.returnDate = date_str
        # 星期几/周几（通用域：更新兼容字段 departDate/returnDate）
        for keyword, wd in self.weekday_keywords.items():
            if keyword in query:
                today_wd = datetime.now().weekday()
                delta = (wd - today_wd) % 7
                target_date = datetime.now() + timedelta(days=delta)
                date_str = target_date.strftime("%Y-%m-%d")
                if "出发" in query or "离开" in query:
                    updated_content.departDate = date_str
                elif "返回" in query or "回来" in query:
                    updated_content.returnDate = date_str
        
        # 提取航班/车次号
        for pattern in self.flight_patterns:
            flight_match = re.search(pattern, query.upper())
            if flight_match:
                updated_content.flightTrainNo = flight_match.group()
                break
        
        train_match = re.search(self.train_pattern, query.upper())
        if train_match:
            updated_content.flightTrainNo = train_match.group()
        
        return updated_content
    
    def _extract_from_query_tmall(self, gpt_content: GptContent, query: str) -> GptContent:
        """从查询中提取信息并更新GPT内容（tmall domain）"""
        updated_content = gpt_content.copy()
        
        # 提取出发地和目的地（按文本位置排序，优先选择具体城市名而非省份别名）
        city_occurrences = []  # [(city, index, length)]
        for city in self.city_mapping.keys():
            idx = query.find(city)
            if idx != -1:
                city_occurrences.append((city, idx, len(city)))
        
        # 按出现顺序排序，但如果位置重叠，优先选择更长的（更具体的）城市名
        city_occurrences.sort(key=lambda x: (x[1], -x[2]))  # 先按位置，再按长度倒序
        
        # 过滤重叠的城市名（选择更具体的）
        filtered_cities = []
        for city, idx, length in city_occurrences:
            # 检查是否与已选择的城市重叠或被包含
            overlaps = False
            for selected_city, selected_idx, selected_length in filtered_cities:
                # 如果两个城市名有重叠区域，选择更长的
                if (idx < selected_idx + selected_length and selected_idx < idx + length):
                    if length > selected_length:
                        # 当前城市更具体，移除之前选择的
                        filtered_cities.remove((selected_city, selected_idx, selected_length))
                    else:
                        # 之前选择的更具体，跳过当前城市
                        overlaps = True
                        break
            
            # 特殊处理：如果当前城市是省份名，且查询中包含该省份+具体城市的组合，则跳过省份名
            if not overlaps and city in ["四川", "广东", "浙江", "江苏", "湖北", "陕西", "河南", "山东", "湖南", "安徽", "福建", "江西", "河北", "山西", "辽宁", "吉林", "黑龙江", "内蒙古", "广西", "海南", "云南", "贵州", "西藏", "甘肃", "青海", "宁夏", "新疆"]:
                # 检查是否存在该省份+城市的组合
                province_city_found = False
                for other_city in self.city_mapping.keys():
                    if other_city != city and city + other_city in query:
                        province_city_found = True
                        break
                if province_city_found:
                    overlaps = True  # 跳过省份名
            
            if not overlaps:
                filtered_cities.append((city, idx, length))
        
        cities_in_query = [c for c, _, _ in sorted(filtered_cities, key=lambda x: x[1])]  # 按出现顺序
        
        if len(cities_in_query) >= 2:
            # 如果有多个城市，根据"从...到..."的模式判断
            if "从" in query and "到" in query:
                # 找到"从"和"到"的位置
                from_index = query.find("从")
                to_index = query.find("到")
                if from_index < to_index:
                    # 提取"从"和"到"之间的城市作为出发地
                    for city in cities_in_query:
                        city_index = query.find(city)
                        if from_index < city_index < to_index:
                            updated_content.org = self.city_mapping[city]
                            break
                    # 提取"到"之后的城市作为目的地
                    for city in cities_in_query:
                        city_index = query.find(city)
                        if city_index > to_index:
                            updated_content.dst = self.city_mapping[city]
                            break
                else:
                    # 如果没有"从...到..."模式，按顺序分配
                    updated_content.org = self.city_mapping[cities_in_query[0]]
                    updated_content.dst = self.city_mapping[cities_in_query[1]]
            else:
                # 按顺序分配
                updated_content.org = self.city_mapping[cities_in_query[0]]
                updated_content.dst = self.city_mapping[cities_in_query[1]]
        elif len(cities_in_query) == 1:
            # 若只命中一个城市，尽量只填补缺失字段，不覆盖已存在的另一个城市；
            # 并利用“到”的位置辅助判断该城市更可能是出发地还是目的地。
            city = cities_in_query[0]
            city_idx = query.find(city)
            idx_from = query.find("从")
            idx_to = query.find("到")  # 覆盖“到达/到”，find会取首次匹配

            def assign_if_empty(field_name: str, value: str):
                current = getattr(updated_content, field_name, None)
                if not current:
                    setattr(updated_content, field_name, value)

            # 优先依据“到”的相对位置判断：城市在“到”之前更可能是出发地；在“到”之后更可能是目的地
            if idx_to != -1 and city_idx != -1:
                if city_idx < idx_to:
                    assign_if_empty("org", self.city_mapping[city])
                else:
                    assign_if_empty("dst", self.city_mapping[city])
            elif idx_from != -1 and city_idx != -1:
                # 有“从”时，该城市在“从”之后通常为出发地
                if city_idx >= idx_from:
                    assign_if_empty("org", self.city_mapping[city])
                else:
                    assign_if_empty("dst", self.city_mapping[city])
            else:
                # 无显式方向词或无法定位时，仅补缺项
                # 检查是否是出发地查询（如"上海的航班"、"从上海出发"等）
                is_departure_query = any(keyword in query for keyword in ["的航班", "出发", "起飞"])
                
                if not updated_content.org and updated_content.dst:
                    assign_if_empty("org", self.city_mapping[city])
                elif updated_content.org and not updated_content.dst:
                    # 如果已有出发地且是出发地查询，不要填充目的地
                    if not is_departure_query:
                        assign_if_empty("dst", self.city_mapping[city])
                elif (not updated_content.org) and (not updated_content.dst):
                    # 都缺时，根据查询类型判断
                    if is_departure_query:
                        assign_if_empty("org", self.city_mapping[city])
                    else:
                        # 默认作为目的地（与既有策略一致）
                        assign_if_empty("dst", self.city_mapping[city])
        
        # 提取日期信息
        # 相对日期（今天/明天/后天...）
        for keyword, days_offset in self.time_keywords.items():
            if keyword in query:
                target_date = datetime.now() + timedelta(days=days_offset)
                date_str = target_date.strftime("%Y-%m-%d")
                if "返回" in query or "回来" in query:
                    updated_content.返回日期 = date_str
                else:
                    # 默认更新出发日期
                    updated_content.出发日期 = date_str
        
        # 提取时间段信息
        for keyword, time_range in self.time_period_keywords.items():
            if keyword in query:
                # 设置departure_time字段
                if hasattr(updated_content, 'departure_time'):
                    updated_content.departure_time = time_range
                else:
                    setattr(updated_content, 'departure_time', time_range)
        # 星期几/周几：更新中文字段
        for keyword, wd in self.weekday_keywords.items():
            if keyword in query:
                today_wd = datetime.now().weekday()
                delta = (wd - today_wd) % 7
                target_date = datetime.now() + timedelta(days=delta)
                date_str = target_date.strftime("%Y-%m-%d")
                if "返回" in query or "回来" in query:
                    updated_content.返回日期 = date_str
                else:
                    # 默认更新出发日期
                    updated_content.出发日期 = date_str
        # 绝对/中文日期抽取（如 9.11 / 9月11日 / 8月20 6点40）
        abs_date = self._extract_date_from_free_text(query)
        if abs_date:
            if "返回" in query or "回来" in query:
                updated_content.返回日期 = abs_date
            else:
                updated_content.出发日期 = abs_date
        
        # 提取航班/车次号（优先匹配更长的模式，避免子串误匹配）
        flight_match = None
        for pattern in self.flight_patterns:
            flight_match = re.search(pattern, query.upper())
            if flight_match:
                break
        train_match = re.search(self.train_pattern, query.upper())
        
        if flight_match and train_match:
            # 如果同时匹配到航班号和车次号，优先选择更长的匹配
            if len(flight_match.group()) >= len(train_match.group()):
                updated_content.航班车次号 = flight_match.group()
                # 提取航空公司代码
                airline_code = self._extract_airline_code(flight_match.group())
                if airline_code:
                    updated_content.airline = airline_code
            else:
                updated_content.航班车次号 = train_match.group()
        elif flight_match:
            updated_content.航班车次号 = flight_match.group()
            # 提取航空公司代码
            airline_code = self._extract_airline_code(flight_match.group())
            if airline_code:
                updated_content.airline = airline_code
        elif train_match:
            updated_content.航班车次号 = train_match.group()
        
        return updated_content

    def _extract_airline_code(self, flight_number: str) -> str:
        """从航班号中提取航空公司代码"""
        if not flight_number:
            return ""
        
        # 标准格式：CA1234 -> CA, MU5678 -> MU
        if re.match(r'[A-Z]{2}\d{3,4}', flight_number):
            return flight_number[:2]
        
        # 数字开头格式：9C9980 -> 9C, 3U8925 -> 3U
        if re.match(r'\d[A-Z]\d{3,4}', flight_number):
            return flight_number[:2]
        
        # 字母数字字母格式：如果有这种格式
        if re.match(r'[A-Z]\d[A-Z]\d{3,4}', flight_number):
            return flight_number[:3]  # 可能需要调整
        
        return ""

    def _extract_date_from_free_text(self, text: str) -> Optional[str]:
        if not text:
            return None
        # 先用通用格式解析，标记为从用户查询提取
        v = self.validate_date_format(text, from_user_query=True)
        if v:
            return v
        # 9.11 / 09.11（兼容全角点）
        m = re.search(r"(\d{1,2})[\.．](\d{1,2})", text)
        if m:
            month, day = int(m.group(1)), int(m.group(2))
            try:
                parsed = datetime(datetime.now().year, month, day)
                # 从用户查询提取的过去日期加一年
                if parsed.date() < datetime.now().date():
                    parsed = parsed.replace(year=parsed.year + 1)
                return parsed.strftime("%Y-%m-%d")
            except Exception:
                pass
        # 8月20日 / 8月20号 / 8月20 6点40（仅取日期）
        m2 = re.search(r"(\d{1,2})\s*月\s*(\d{1,2})\s*(日|号)?", text)
        if m2:
            month, day = int(m2.group(1)), int(m2.group(2))
            try:
                parsed = datetime(datetime.now().year, month, day)
                # 从用户查询提取的过去日期加一年
                if parsed.date() < datetime.now().date():
                    parsed = parsed.replace(year=parsed.year + 1)
                return parsed.strftime("%Y-%m-%d")
            except Exception:
                pass
        return None

    def _extract_latest_date_from_history(self, history: List[UserMessage]) -> Optional[str]:
        """从历史消息中提取最新的日期（基于文本匹配时间概念）并转为 YYYY-MM-DD"""
        if not history:
            return None
        # 从后往前找，命中即返回
        for msg in reversed(history):
            text = getattr(msg, 'content', '') or ''
            # 相对日期优先
            for k, offset in self.time_keywords.items():
                if k in text:
                    target = datetime.now() + timedelta(days=offset)
                    return target.strftime('%Y-%m-%d')
            # 星期几/周几
            for k, wd in getattr(self, 'weekday_keywords', {}).items():
                if k in text:
                    today_wd = datetime.now().weekday()
                    delta = (wd - today_wd) % 7
                    target = datetime.now() + timedelta(days=delta)
                    return target.strftime('%Y-%m-%d')
            # 绝对日期模式
            patterns = [r"\b\d{4}-\d{2}-\d{2}\b", r"\b\d{4}/\d{2}/\d{2}\b", r"\b\d{2}-\d{2}\b", r"\b\d{2}/\d{2}\b"]
            for p in patterns:
                m = re.search(p, text)
                if m:
                    ds = m.group(0)
                    if re.match(r"\d{2}-\d{2}", ds):
                        ds = f"{datetime.now().year}-{ds}"
                    if re.match(r"\d{2}/\d{2}", ds):
                        ds = f"{datetime.now().year}/{ds}"
                    try:
                        return parser.parse(ds).strftime('%Y-%m-%d')
                    except Exception:
                        continue
        return None
    
    def check_gpt_content_conflicts(self, current_gpt: GptContent, history_gpt_list: List[GptContent]) -> List[ParameterConflict]:
        """检查GPT内容冲突"""
        conflicts = []
        
        if not history_gpt_list:
            return conflicts
        
        # 获取最近的GPT内容
        latest_history = history_gpt_list[-1]
        
        # 检查核心字段冲突
        core_fields = ["org", "dst", "departDate", "returnDate", "flightTrainNo"]
        
        for field_name in core_fields:
            current_value = getattr(current_gpt, field_name, None)
            history_value = getattr(latest_history, field_name, None)
            
            if current_value and history_value and current_value != history_value:
                conflict_type = self._determine_conflict_type(current_value, history_value)
                
                conflicts.append(ParameterConflict(
                    slot_name=field_name,
                    current_value=current_value,
                    history_value=history_value,
                    conflict_type=conflict_type
                ))
        
        return conflicts
    
    def resolve_gpt_content_conflicts(self, conflicts: List[ParameterConflict], query: str) -> Dict[str, Any]:
        """解决GPT内容冲突"""
        resolutions = {}
        
        for conflict in conflicts:
            # 检查查询中是否有明确的修改意图
            if self._has_explicit_modification(query, conflict.slot_name):
                conflict.conflict_type = "explicit"
                resolutions[conflict.slot_name] = conflict.current_value
            else:
                # 隐含修改，需要澄清
                conflict.resolution = "need_clarification"
                resolutions[conflict.slot_name] = None
        
        return resolutions
    
    def _has_explicit_modification(self, query: str, slot_name: str) -> bool:
        """检查是否有明确的修改意图"""
        modification_keywords = {
            "org": ["改", "换", "修改", "变更", "调整", "不是", "错了", "应该是"],
            "dst": ["改", "换", "修改", "变更", "调整", "不是", "错了", "应该是"],
            "departDate": ["改", "换", "修改", "变更", "调整"],
            "returnDate": ["改", "换", "修改", "变更", "调整"],
            "flightTrainNo": ["不是", "错了", "应该是", "改", "换"]
        }
        
        keywords = modification_keywords.get(slot_name, [])
        return any(keyword in query for keyword in keywords)
    
    def generate_gpt_clarification_message(self, conflicts: List[ParameterConflict]) -> str:
        """为GPT内容生成澄清消息"""
        if not conflicts:
            return ""
        
        messages = []
        for conflict in conflicts:
            if conflict.conflict_type == "implicit":
                slot_name_map = {
                    "org": "出发地",
                    "dst": "目的地",
                    "departDate": "出发日期",
                    "returnDate": "返回日期",
                    "flightTrainNo": "航班/车次号"
                }
                
                slot_display = slot_name_map.get(conflict.slot_name, conflict.slot_name)
                messages.append(
                    f"您之前说{slot_display}是{conflict.history_value}，"
                    f"现在要改为{conflict.current_value}吗？"
                )
        
        return "；".join(messages) if messages else ""
    
    def simplify_airport_name(self, airport_text: str) -> str:
        """简化机场名称，去掉详细的航站楼信息"""
        if not airport_text:
            return ""
        
        # 白云机场T2航站楼A区 -> 白云机场
        # 首都机场T3航站楼 -> 首都机场
        import re
        pattern = r"(.+机场)(?:T\d+)?(?:航站楼)?(?:[AB]区)?"
        match = re.match(pattern, airport_text)
        return match.group(1) if match else airport_text
    
    def clear_location_fields_for_info_query(self, gpt_content: GptContent) -> GptContent:
        """为信息查询清空位置和时间字段"""
        updated_content = gpt_content.copy()
        
        # 清空位置字段
        updated_content.org = ""
        updated_content.dst = ""
        
        # 清空时间字段
        updated_content.出发日期 = ""
        updated_content.返回日期 = ""
        if hasattr(updated_content, 'departDate'):
            updated_content.departDate = ""
        if hasattr(updated_content, 'returnDate'):
            updated_content.returnDate = ""
        
        # 清空航班车次号
        updated_content.航班车次号 = ""
        if hasattr(updated_content, 'flightTrainNo'):
            updated_content.flightTrainNo = ""
        
        return updated_content
    
    def process_airport_field(self, gpt_content: GptContent, query: str) -> GptContent:
        """处理机场字段，简化机场名称"""
        updated_content = gpt_content.copy()
        
        # 如果有airport字段，进行简化
        if hasattr(updated_content, 'airport') and updated_content.airport:
            updated_content.airport = self.simplify_airport_name(updated_content.airport)
        
        return updated_content