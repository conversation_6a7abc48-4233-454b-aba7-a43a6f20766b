import os
import json
import tempfile
from typing import List, Dict, Optional
from core.models import IntentCandidate


class RasaClassifier:
    """Rasa意图分类器"""
    
    def __init__(self, domain: str = "flight"):
        self.domain = domain
        self.model_path = f"models/{domain}_intent_model"
        self.training_data_path = f"data/{domain}_training_data.yml"
        self.intents = self._get_domain_intents()
        
    def _get_domain_intents(self) -> List[str]:
        """获取域相关的意图列表"""
        # tmall 域仅支持 flights / train / normal 三类
        if self.domain == "tmall":
            return ["flights", "train", "normal"]
        # 其他域按原有示例（仅作为占位，不影响 tmall）
        if self.domain == "flight":
            return ["查询航班", "预订航班", "通用agent"]
        if self.domain == "train":
            return ["查询列车", "预订列车", "通用agent"]
        return ["通用agent"]
    
    def create_training_data(self, examples: Dict[str, List[str]]):
        """创建训练数据文件"""
        os.makedirs("data", exist_ok=True)
        
        training_data = {
            "version": "2.0",
            "nlu": []
        }
        
        for intent, intent_examples in examples.items():
            if intent in self.intents:
                training_data["nlu"].append({
                    "intent": intent,
                    "examples": intent_examples
                })
        
        # 写入YAML文件
        with open(self.training_data_path, 'w', encoding='utf-8') as f:
            f.write("version: '2.0'\n")
            f.write("nlu:\n")
            
            for intent_data in training_data["nlu"]:
                f.write(f"- intent: {intent_data['intent']}\n")
                f.write("  examples: |\n")
                for example in intent_data['examples']:
                    f.write(f"    - {example}\n")
    
    def train_model(self):
        """训练Rasa模型"""
        if not os.path.exists(self.training_data_path):
            # 创建默认训练数据
            default_examples = {
                "询问时间": [
                    "明天下午", "您大概几点", "您晚餐还是午餐",
                    "嗯周三是吗女士", "您大约几点钟能来", "下午几点",
                    "您是中午还是下午", "你六点过来是吗", "预订几点的"
                ],
                "查询航班": [
                    "查询航班", "什么时候的航班", "航班信息",
                    "CA1234航班", "MU5678什么时候起飞"
                ],
                "预订航班": [
                    "预订航班", "订票", "买票", "预约航班",
                    "帮我订一张票", "想订明天的航班"
                ],
                "延误登机": [
                    "航班延误", "CA1234延误了", "晚点了",
                    "飞机延误", "什么时候能起飞"
                ],
                "通用agent": [
                    "改签", "重新预定", "取消订单", "退票",
                    "修改订单", "变更航班"
                ]
            }
            self.create_training_data(default_examples)
        
        # 这里应该调用Rasa训练命令
        # 由于Rasa需要完整的项目结构，这里只是示例
        print(f"训练数据已创建: {self.training_data_path}")
        print("注意：实际使用时需要配置完整的Rasa项目")
    
    def predict_intent(self, query: str, slot_info: Dict = None) -> List[IntentCandidate]:
        """预测意图"""
        # 构建查询文本
        if slot_info:
            slot_text = " ".join([f"{k}={v}" for k, v in slot_info.items() if v])
            full_query = f"query: {query} slot: {slot_text}"
        else:
            full_query = query
        
        # 这里应该调用训练好的Rasa模型进行预测
        # 由于Rasa需要完整的项目结构，这里返回模拟结果
        candidates = []
        
        # tmall 域只输出 flights / train / normal
        if self.domain == "tmall":
            text = (query or "") + (" " + slot_text if slot_info else "")
            text_upper = text.upper()
            # 简单规则：出现车次字母 或 "火车/高铁/动车/列车" 判为 train
            if any(k in text for k in ["火车", "高铁", "动车", "列车"]) or re_search(r"\b[GDTCZKXYL]\d{1,4}\b", text_upper):
                candidates.append(IntentCandidate(intent="train", confidence=0.85, source="rasa"))
            # 出现航司两字母/航班、机票等关键词 判为 flights
            elif any(k in text for k in ["航班", "机票", "飞机"]) or re_search(r"\b[A-Z]{2}\d{3,4}\b", text_upper):
                candidates.append(IntentCandidate(intent="flights", confidence=0.85, source="rasa"))
            else:
                candidates.append(IntentCandidate(intent="normal", confidence=0.70, source="rasa"))
            return candidates
        
        # 非 tmall 的占位模拟
        if "查询" in query or "什么时候" in query:
            candidates.append(IntentCandidate(intent="查询航班", confidence=0.85, source="rasa"))
        elif "预订" in query or "订票" in query:
            candidates.append(IntentCandidate(intent="预订航班", confidence=0.90, source="rasa"))
        elif "改签" in query or "取消" in query:
            candidates.append(IntentCandidate(intent="通用agent", confidence=0.80, source="rasa"))
        else:
            candidates.append(IntentCandidate(intent="通用agent", confidence=0.60, source="rasa"))
        
        return candidates
    
    def get_intent_confidence(self, query: str, intent: str) -> float:
        """获取特定意图的置信度"""
        candidates = self.predict_intent(query)
        for candidate in candidates:
            if candidate.intent == intent:
                return candidate.confidence
        return 0.0
    
    def is_model_trained(self) -> bool:
        """检查模型是否已训练"""
        return os.path.exists(self.model_path)
    
    def load_model(self):
        """加载训练好的模型"""
        if not self.is_model_trained():
            self.train_model()
        print(f"模型已加载: {self.model_path}") 