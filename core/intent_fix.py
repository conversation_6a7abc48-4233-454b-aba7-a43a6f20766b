from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from core.models import (
    IntentFixInput, IntentFixOutput, GptContent, SlotInfo, 
    UserMessage, IntentCandidate, ParameterConflict
)
from core.rule_engine import RuleEngine
from core.parameter_validator import ParameterValidator


class IntentFixEngine:
    """意图修正引擎主流程"""
    
    def __init__(self, domain: str = "flight"):
        self.domain = domain
        self.rule_engine = RuleEngine()
        self.parameter_validator = ParameterValidator()
        
        # 意图置信度阈值
        self.confidence_threshold = 0.7
        self.clarification_threshold = 0.5
        
    def fix_intent(self, input_data: IntentFixInput) -> IntentFixOutput:
        """主流程：意图修正"""
        
        # 1. 提取当前用户请求
        current_query = self._extract_current_query(input_data.userHistory)
        previous_intent = self._extract_previous_intent(input_data.slot, input_data.gptcontent)
        
        # 2. 规则驱动校验
        rule_result = self.rule_engine.apply_rules(
            current_query, 
            self._convert_history_to_dict(input_data.userHistory)
        )
        
        # 3. 结果比对与初步判定
        intent_candidates = self._compare_results(
            rule_result, 
            input_data.gptcontent.intent, 
            previous_intent
        )
        
        # 4. 取消 Rasa 模型纠正逻辑（仅基于规则与输入意图）
        
        # 5. 参数校验修正
        validated_slot = self._validate_and_fix_parameters(
            input_data.slot, 
            current_query, 
            input_data.userHistory
        )
        
        # 6. GPT内容校验修正
        validated_gpt_content = self._validate_and_fix_gpt_content(
            input_data.gptcontent,
            current_query,
            input_data.userHistory,
            input_data.domain
        )
        
        # 6. 多轮澄清确认
        final_intent, clarification_message = self._determine_final_intent(
            intent_candidates, 
            current_query
        )
        
        # 特殊处理：航班跟进查询
        if final_intent == "flights" and rule_result and "航班查询跟进" in rule_result.matched_patterns:
            # 从slot中提取航班号
            slot_flight_number = getattr(input_data.slot, '航班车次号', None) or getattr(input_data.slot, 'flightTrainNo', None)
            if slot_flight_number:
                # 更新validated_gpt_content中的航班号
                setattr(validated_gpt_content, '航班车次号', slot_flight_number)
            
            # 从slot中继承城市信息（如果当前gpt_content中缺失）
            slot_org = getattr(input_data.slot, 'org', None)
            slot_dst = getattr(input_data.slot, 'dst', None)
            if slot_org and not validated_gpt_content.org:
                validated_gpt_content.org = slot_org
            if slot_dst and not validated_gpt_content.dst:
                validated_gpt_content.dst = slot_dst
            
            # 处理时间相关的跟进
            if "明天" in current_query:
                from datetime import datetime, timedelta
                tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
                setattr(validated_gpt_content, '出发日期', tomorrow)
            elif "后天" in current_query:
                from datetime import datetime, timedelta
                day_after_tomorrow = (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d")
                setattr(validated_gpt_content, '出发日期', day_after_tomorrow)

        # 首轮兜底规则（tmall）：既无出发地/目的地，也无航班/车次号，则默认 normal
        # 但不要覆盖规则引擎的高置信度结果（如航班/车次信息查询）
        try:
            if input_data.domain == "tmall" and len(input_data.userHistory) <= 1:
                # 如果规则引擎返回了高置信度结果，不要覆盖
                if rule_result and rule_result.confidence >= 1.0:
                    pass  # 跳过兜底规则
                else:
                    no_cities = not (getattr(validated_gpt_content, 'org', None) or getattr(validated_gpt_content, 'dst', None))
                    no_numbers = not (
                        getattr(validated_gpt_content, '航班车次号', None) or
                        getattr(validated_gpt_content, 'flightTrainNo', None)
                    )
                    if no_cities and no_numbers:
                        final_intent = "normal"
        except Exception:
            pass
        
        # 7. 构建输出
        output_gptcontent = self._build_output_gptcontent(
            final_intent, 
            validated_slot, 
            validated_gpt_content,
            input_data.domain,
            rule_result,
            current_query
        )
        
        return IntentFixOutput(
            gptcontent=output_gptcontent,
            resultReply=clarification_message
        )
    
    def _extract_current_query(self, user_history: List[UserMessage]) -> str:
        """提取当前用户请求"""
        if not user_history:
            return ""
        
        # 获取最后一条用户消息
        for msg in reversed(user_history):
            if msg.role == "user":
                return msg.content
        
        return ""
    
    def _extract_previous_intent(self, slot: SlotInfo, gptcontent: GptContent) -> Optional[str]:
        """提取上一轮识别的意图"""
        # 优先使用slot中的意图，其次使用gptcontent中的意图
        if slot.intent:
            return slot.intent
        elif gptcontent.intent:
            return gptcontent.intent
        return None
    
    def _convert_history_to_dict(self, user_history: List[UserMessage]) -> List[Dict]:
        """转换历史记录为字典格式"""
        history_dict = []
        for msg in user_history:
            history_dict.append({
                'role': msg.role,
                'content': msg.content,
                'time': msg.time,
                'type': msg.type
            })
        return history_dict
    
    def _convert_slot_to_dict(self, slot: SlotInfo) -> Dict[str, Any]:
        """转换槽位信息为字典格式"""
        slot_dict = {}
        # 使用SlotInfo模型中实际存在的字段
        field_names = ["departDate", "mainBusinessType", "出发日期", "返回日期", "主业务类型", "航班车次号", "org", "dst"]
        for field_name in field_names:
            value = getattr(slot, field_name, None)
            if value:
                slot_dict[field_name] = value
        return slot_dict
    
    def _compare_results(self, rule_result, gpt_intent: str, previous_intent: Optional[str]) -> List[IntentCandidate]:
        """结果比对与初步判定"""
        candidates = []
        
        # 添加规则结果
        if rule_result.intent and rule_result.confidence > 0:
            candidates.append(IntentCandidate(
                intent=rule_result.intent,
                confidence=rule_result.confidence,
                source="rule"
            ))
        
        # 添加GPT模型结果
        if gpt_intent:
            gpt_confidence = 0.8  # 默认置信度
            candidates.append(IntentCandidate(
                intent=gpt_intent,
                confidence=gpt_confidence,
                source="model"
            ))
        
        # 添加上一轮意图（如果存在且与当前不同）
        if previous_intent and previous_intent != gpt_intent:
            candidates.append(IntentCandidate(
                intent=previous_intent,
                confidence=0.6,
                source="history"
            ))
        
        return candidates
    
    def _validate_and_fix_parameters(self, slot: SlotInfo, query: str, user_history: List[UserMessage]) -> SlotInfo:
        """参数校验修正"""
        # 1. 参数规范性校验
        validated_slot = self._validate_slot_parameters(slot)
        
        # 2. 参数一致性校验
        history_slots = self._extract_history_slots(user_history)
        conflicts = self.parameter_validator.check_parameter_conflicts(validated_slot, history_slots)
        
        if conflicts:
            resolutions = self.parameter_validator.resolve_conflicts(conflicts, query)
            validated_slot = self._apply_conflict_resolutions(validated_slot, resolutions)
        
        # 3. 参数动态更新机制
        updated_slot = self.parameter_validator.update_slot_values(validated_slot, query)
        
        return updated_slot
    
    def _validate_and_fix_gpt_content(self, gpt_content: GptContent, query: str, user_history: List[UserMessage], domain: str = "tmall") -> GptContent:
        """GPT内容校验修正"""
        # 1. 核心字段校验（传入历史）
        validated_gpt = self.parameter_validator.validate_gpt_content(gpt_content, query, domain, user_history)
        
        # 2. 参数一致性校验
        history_gpt_list = self._extract_history_gpt_content(user_history)
        conflicts = self.parameter_validator.check_gpt_content_conflicts(validated_gpt, history_gpt_list)
        
        if conflicts:
            resolutions = self.parameter_validator.resolve_gpt_content_conflicts(conflicts, query)
            validated_gpt = self._apply_gpt_conflict_resolutions(validated_gpt, resolutions)
        
        return validated_gpt
    
    def _validate_slot_parameters(self, slot: SlotInfo) -> SlotInfo:
        """校验槽位参数"""
        validated_slot = slot.copy()
        
        # 校验各个槽位
        slot_fields = ["departDate"]
        for field_name in slot_fields:
            value = getattr(slot, field_name, None)
            if value:
                validated_value, is_valid = self.parameter_validator.validate_slot_value(field_name, value)
                if is_valid:
                    setattr(validated_slot, field_name, validated_value)
        
        return validated_slot
    
    def _extract_history_slots(self, user_history: List[UserMessage]) -> List[SlotInfo]:
        """从历史记录中提取槽位信息"""
        # 这里应该从历史记录中提取槽位信息
        # 简化实现：返回空列表
        return []
    
    def _apply_conflict_resolutions(self, slot: SlotInfo, resolutions: Dict[str, Any]) -> SlotInfo:
        """应用冲突解决方案"""
        updated_slot = slot.copy()
        
        for slot_name, value in resolutions.items():
            if value is not None:
                setattr(updated_slot, slot_name, value)
        
        return updated_slot
    
    def _extract_history_gpt_content(self, user_history: List[UserMessage]) -> List[GptContent]:
        """从历史记录中提取GPT内容"""
        # 这里应该从历史记录中提取GPT内容
        # 简化实现：返回空列表
        return []
    
    def _apply_gpt_conflict_resolutions(self, gpt_content: GptContent, resolutions: Dict[str, Any]) -> GptContent:
        """应用GPT内容冲突解决方案"""
        updated_gpt = gpt_content.copy()
        
        for field_name, value in resolutions.items():
            if value is not None:
                setattr(updated_gpt, field_name, value)
        
        return updated_gpt
    
    def _determine_final_intent(self, candidates: List[IntentCandidate], query: str) -> Tuple[str, Optional[str]]:
        """确定最终意图"""
        if not candidates:
            return "通用agent", None
        
        # 按置信度排序
        sorted_candidates = sorted(candidates, key=lambda x: x.confidence, reverse=True)
        
        # 如果最高置信度超过阈值，直接返回
        if sorted_candidates[0].confidence >= self.confidence_threshold:
            return sorted_candidates[0].intent, None
        
        # 如果多个候选意图置信度接近，需要澄清
        top_candidates = [c for c in sorted_candidates if c.confidence >= self.clarification_threshold]
        
        if len(top_candidates) > 1:
            clarification_message = self._generate_clarification_message(top_candidates)
            return "通用agent", clarification_message
        
        # 如果所有候选置信度都很低，返回通用agent
        return "通用agent", None
    
    def _generate_clarification_message(self, candidates: List[IntentCandidate]) -> str:
        """生成澄清消息"""
        intent_names = [c.intent for c in candidates]
        intent_display = "、".join(intent_names)
        return f"您是想要{intent_display}还是其他问题？"
    
    def _build_output_gptcontent(self, final_intent: str, validated_slot: SlotInfo, validated_gptcontent: GptContent, domain: str = "tmall", rule_result=None, current_query: str = "") -> GptContent:
        """构建输出GPT内容"""
        output_dict = {
            "intent": final_intent
        }
        
        if domain == "tmall":
            # tmall domain格式 - 只包含中文字段；日期优先采用 slot 的值
            # 航班/车次号 - 优先使用规则引擎提取的号码
            extracted_number = rule_result.extracted_number if rule_result else None
            flight_number = extracted_number or getattr(validated_gptcontent, '航班车次号', None) or ""
            
            # 对于航班跟进查询，从validated_gptcontent中获取航班号（已在fix_intent中设置）
            if final_intent == "flights" and rule_result and "航班查询跟进" in rule_result.matched_patterns:
                flight_number = getattr(validated_gptcontent, '航班车次号', None) or flight_number
            
            output_dict["航班车次号"] = flight_number
            # org/dst
            output_dict["dst"] = validated_gptcontent.dst or ""
            output_dict["org"] = validated_gptcontent.org or ""
            # 出发/返回日期（优先使用经过校验/历史融合后的 gptcontent，其次才回退 slot）
            slot_depart_cn = getattr(validated_slot, "出发日期", None) or getattr(validated_slot, "departDate", None)
            slot_return_cn = getattr(validated_slot, "返回日期", None) or getattr(validated_slot, "returnDate", None)
            output_dict["出发日期"] = (validated_gptcontent.出发日期 or getattr(validated_gptcontent, "departDate", None) or slot_depart_cn or "")
            output_dict["返回日期"] = (validated_gptcontent.返回日期 or getattr(validated_gptcontent, "returnDate", None) or slot_return_cn or "")
            # 其他字段
            output_dict["questions"] = validated_gptcontent.questions or []
            output_dict["itinerary_num"] = validated_gptcontent.itinerary_num or ""
            # 主业务类型与最终意图对齐（优先）：train -> 火车票；flights -> 机票；否则保留已有
            if final_intent == "train":
                output_dict["主业务类型"] = "火车票"
            elif final_intent == "flights":
                output_dict["主业务类型"] = "机票"
            else:
                output_dict["主业务类型"] = validated_gptcontent.主业务类型 or ""
            output_dict["airport"] = validated_gptcontent.airport or ""
            
            # 特殊处理 departure_time 字段
            departure_time = getattr(validated_gptcontent, 'departure_time', None)
            if departure_time:
                output_dict["departure_time"] = departure_time
            
            # 处理航空公司代码字段 - 从输入中获取或从航班号中提取
            airline = getattr(validated_gptcontent, 'airline', None)
            # 如果validated_gptcontent中没有airline，尝试从原始输入中获取
            if not airline:
                # 从原始gptcontent的parameters中获取airline
                original_airline = getattr(validated_gptcontent, 'parameters', {}).get('airline', None) if hasattr(validated_gptcontent, 'parameters') else None
                if original_airline:
                    airline = original_airline
            # 如果还是没有airline但有航班号，尝试从航班号中提取
            if not airline and flight_number:
                airline = self.parameter_validator._extract_airline_code(flight_number)
            if airline:
                output_dict["airline"] = airline
            
            # 对于normal意图（信息查询），需要特殊处理
            if final_intent == "normal":
                # 清空位置和时间字段，但保留机场和问题信息
                output_dict["org"] = ""
                output_dict["dst"] = ""
                output_dict["出发日期"] = ""
                output_dict["返回日期"] = ""
                # 对于机场信息查询，清空航班/车次号；对于其他normal查询保持原值
                if rule_result and "机场信息查询" in rule_result.matched_patterns:
                    output_dict["航班车次号"] = ""
                
                # 对于交通工具分类查询，生成相关问题
                if rule_result and "交通工具分类查询" in rule_result.matched_patterns:
                    output_dict["航班车次号"] = ""
                    output_dict["questions"] = [
                        "高铁和火车有什么区别？",
                        "高铁是否属于火车的一种？",
                        "高速铁路与普通铁路的分类关系是什么？",
                        "高铁在交通工具分类中属于哪一类？",
                        "高铁和动车、普通火车的关系是什么？"
                    ]
                
                # 对于优惠政策查询，生成相关问题
                if rule_result and "优惠政策查询" in rule_result.matched_patterns:
                    output_dict["航班车次号"] = ""
                    # 根据查询内容生成相应的问题
                    if "老人" in current_query or "60" in current_query or "岁" in current_query:
                        output_dict["questions"] = [
                            "60岁以上老人购买高铁票有优惠吗？",
                            "老年人乘坐高铁有票价优惠政策吗？",
                            "高铁对老年乘客有特殊票价吗？",
                            "60岁以上老人坐高铁能享受折扣吗？",
                            "老年人高铁票价有减免政策吗？"
                        ]
                    elif "儿童" in current_query or "小孩" in current_query:
                        output_dict["questions"] = [
                            "儿童购买高铁票有优惠吗？",
                            "多大的孩子可以享受高铁儿童票？",
                            "高铁儿童票价格是多少？",
                            "儿童乘坐高铁需要什么证件？",
                            "高铁儿童票优惠政策是什么？"
                        ]
                    elif "学生" in current_query:
                        output_dict["questions"] = [
                            "学生购买高铁票有优惠吗？",
                            "高铁学生票如何购买？",
                            "学生证可以买高铁优惠票吗？",
                            "高铁学生票价格是多少？",
                            "学生乘坐高铁有什么优惠政策？"
                        ]
                    elif "军人" in current_query:
                        output_dict["questions"] = [
                            "军人购买高铁票有优惠吗？",
                            "现役军人坐高铁有票价减免吗？",
                            "军人证可以买高铁优惠票吗？",
                            "部队人员乘坐高铁有优惠待遇吗？",
                            "军人高铁票优惠政策是什么？"
                        ]
                    else:
                        # 通用优惠政策问题
                        output_dict["questions"] = [
                            "高铁票有哪些优惠政策？",
                            "什么人群可以享受高铁票优惠？",
                            "高铁票价优惠条件是什么？",
                            "如何购买高铁优惠票？",
                            "高铁票优惠政策有哪些规定？"
                        ]
                
                # 对于中转服务咨询，生成相关问题
                if rule_result and "中转服务咨询" in rule_result.matched_patterns:
                    output_dict["航班车次号"] = ""
                    if "停留" in current_query and "小时" in current_query:
                        output_dict["questions"] = [
                            "中转机场停留16小时有什么服务安排？",
                            "长时间中转停留机场提供哪些便民服务？",
                            "航空公司对长时间中转旅客有什么安排？",
                            "机场中转区有休息或住宿服务吗？",
                            "中转停留期间可以享受哪些免费服务？"
                        ]
                    else:
                        output_dict["questions"] = [
                            "机场中转服务有哪些？",
                            "中转旅客可以享受什么服务？",
                            "机场为中转乘客提供哪些便利？",
                            "中转区有什么设施和服务？",
                            "航空公司中转服务政策是什么？"
                        ]
                
                # 对于信息咨询，生成通用问题
                if rule_result and "信息咨询" in rule_result.matched_patterns:
                    output_dict["航班车次号"] = ""
                    output_dict["questions"] = [
                        "相关政策规定是什么？",
                        "具体要求和条件有哪些？",
                        "办理流程是怎样的？",
                        "需要准备哪些材料？",
                        "有什么注意事项？"
                    ]
            
            # 对于航班跟进查询，需要特殊处理 - 确保航班号不被清空
            if final_intent == "flights" and rule_result and "航班查询跟进" in rule_result.matched_patterns:
                # 航班号应该已经在前面的特殊处理中设置了，这里确保不被覆盖
                pass
                
                # 简化机场名称
                if output_dict["airport"]:
                    output_dict["airport"] = self.parameter_validator.simplify_airport_name(output_dict["airport"])
                
                # 保留questions数组（如果没有被上面的特殊处理覆盖）
                if validated_gptcontent.questions and "交通工具分类查询" not in (rule_result.matched_patterns if rule_result else []):
                    output_dict["questions"] = validated_gptcontent.questions
            
            # 不包含英文字段（departDate, returnDate, flightTrainNo等）
            # 这些字段在tmall domain中应该为None，不会出现在输出中
        else:
            # 其他domain格式
            # 添加主业务类型
            if validated_slot.mainBusinessType:
                output_dict["mainBusinessType"] = validated_slot.mainBusinessType
            elif validated_gptcontent.mainBusinessType:
                output_dict["mainBusinessType"] = validated_gptcontent.mainBusinessType
            
            # 添加核心校验字段
            core_fields = ["org", "dst", "departDate", "returnDate", "flightTrainNo", "mainBusinessType"]
            for field_name in core_fields:
                value = getattr(validated_gptcontent, field_name, None)
                if value:
                    output_dict[field_name] = value
            
            # 添加兼容字段
            compatible_fields = ["出发日期", "返回日期", "航班车次号", "主业务类型"]
            for field_name in compatible_fields:
                value = getattr(validated_gptcontent, field_name, None)
                if value:
                    output_dict[field_name] = value
            
            # 添加其他字段（直接返回）
            other_fields = ["questions", "itinerary_num", "airport"]
            for field_name in other_fields:
                value = getattr(validated_gptcontent, field_name, None)
                if value is not None:
                    output_dict[field_name] = value
        
        if domain == "tmall":
            # 对于tmall domain，排除None值的英文字段
            filtered_dict = {}
            for key, value in output_dict.items():
                if key in ["departDate", "returnDate", "flightTrainNo", "mainBusinessType"] and value is None:
                    continue
                filtered_dict[key] = value
            return GptContent(**filtered_dict)
        else:
            return GptContent(**output_dict)
    
    def add_custom_rules(self, domain: str, keyword_rules: Dict[str, List[str]] = None, pattern_rules: Dict[str, List[str]] = None):
        """添加自定义规则"""
        if keyword_rules:
            for intent, keywords in keyword_rules.items():
                self.rule_engine.add_keyword_rule(intent, keywords)
        
        if pattern_rules:
            for intent, patterns in pattern_rules.items():
                self.rule_engine.add_pattern_rule(intent, patterns)
    
    def set_confidence_thresholds(self, confidence_threshold: float = 0.7, clarification_threshold: float = 0.5):
        """设置置信度阈值"""
        self.confidence_threshold = confidence_threshold
        self.clarification_threshold = clarification_threshold 