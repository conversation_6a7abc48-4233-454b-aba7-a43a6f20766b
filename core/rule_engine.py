import re
import jieba
from typing import Dict, List, Optional, Tuple
from core.models import RuleResult


class RuleEngine:
    """规则引擎，负责基于规则的意图识别
    - 可按域扩展：通过 add_keyword_rule / add_pattern_rule 添加域特定规则
    - 目前默认规则适配 domain=tmall，仅输出 flights / train / normal
    """
    
    def __init__(self):
        # 关键词级别映射规则
        self.keyword_rules = {
            "flights": [
                "航班", "飞机", "航空", "机票", "飞机票", "登机", "起飞", "到达",
                "出发", "到达", "到", "从", "飞", "航班号", "CA", "MU", "KN", "CZ",
                # 扩展的长关键词
                "机票多少钱", "机票余票", "机票价格", "机票查询", "机票预订",
                "飞机票多少钱", "飞机票余票", "飞机票价格", "飞机票查询",
                "航班查询", "航班信息", "航班状态", "航班延误", "航班取消",
                "登机时间", "起飞时间", "到达时间", "航班号查询",
                "机票改签", "机票退票", "机票取消", "机票变更"
            ],
            "train": [
                "火车", "列车", "高铁", "动车", "车票", "发车", "到达", "出发",
                "到", "从", "车次",
                # 扩展的长关键词
                "火车票多少钱", "火车票余票", "火车票价格", "火车票查询", "火车票预订",
                "高铁票多少钱", "高铁票余票", "高铁票价格", "高铁票查询",
                "动车票多少钱", "动车票余票", "动车票价格", "动车票查询",
                "二等座", "一等座", "商务座", "硬座", "软座", "硬卧", "软卧",
                "火车票改签", "火车票退票", "火车票取消", "火车票变更",
                "车次查询", "车次信息", "车次状态", "发车时间", "到达时间"
            ],
            "normal": [
                # tmall 仅保留通用类关键词
                "改签", "取消", "退票", "退款", "修改", "变更", "调整",
                "订单", "预订", "预约", "订票", "买票", "天气", "气温", "温度",
                "价格", "多少钱", "规则", "方法", "说明", "流程"
            ]
        }
        
        # 模板级规则（正则表达式）
        self.pattern_rules = {
            "flights": [
                r".*\b[A-Z]{2}\d{3,4}\b.*",     # 标准航班号模式：CA1234, MU5678, JD5128
                r".*\b\d[A-Z]\d{3,4}\b.*",      # 数字开头航班号：9C9980, 3U8925
                r".*\b[A-Z]\d[A-Z]\d{3,4}\b.*", # 字母数字字母模式
                r".*从.*到.*航班.*",        # 出发地到到达地航班
                r".*从.*到.*飞机.*",        # 出发地到到达地飞机
                r".*白天.*出发.*飞机.*",    # 白天出发飞机
            ],
            "train": [
                r".*\b[GDTCKZXYL]\d{1,4}\b.*", # 车次号模式（G、D、T、K、C、Z、X、Y、L开头）
                r".*从.*到.*火车.*",        # 出发地到到达地火车
                r".*从.*到.*高铁.*",        # 出发地到到达地高铁
                r".*从.*到.*动车.*",        # 出发地到到达地动车

            ],
            "normal": [
                r".*改签.*",
                r".*退票.*",
                r".*取消.*订单.*",
                r".*天气.*",
                r".*温度.*",
                # 扩展的长模式
                r".*价格.*查询.*",          # 价格查询
                r".*价格.*多少.*",          # 价格询问
                r".*多少钱.*",              # 价格询问
                r".*费用.*查询.*",          # 费用查询
                r".*费用.*多少.*",          # 费用询问
                r".*时间.*查询.*",          # 时间查询
                r".*时间.*安排.*",          # 时间安排
                r".*时刻表.*",              # 时刻表
                r".*班次.*查询.*",          # 班次查询
                r".*班次.*信息.*",          # 班次信息
            ]
        }
        
        # 机场信息查询关键词
        self.airport_info_keywords = [
            "区别", "不同", "差异", "区分", "功能", "设施", "航站楼", "候机楼",
            "服务", "便利", "差别", "对比", "比较", "分别", "各自"
        ]
        
        # 机场信息查询模式
        self.airport_info_patterns = [
            r"(.+机场).*(A区|B区|T\d+).*区别",
            r"(.+机场).*(航站楼|候机楼).*不同",
            r"(.+机场).*设施.*差异",
            r"(.+机场).*(A区|B区).*功能",
            r"(.+机场).*(T\d+).*区别"
        ]
        
        # 航班信息查询关键词
        self.flight_info_keywords = [
            "大飞机", "小飞机", "机型", "餐食", "正餐", "餐", "飞机大小", "机型大小"
        ]
        
        # 车次信息查询关键词  
        self.train_info_keywords = [
            "网", "WiFi", "餐车", "服务", "上网", "无线网", "网络"
        ]
        
        # 交通工具分类查询关键词
        self.transport_classification_keywords = [
            "属于", "是否属于", "是不是", "算不算", "分类", "类型", "种类", "关系",
            "区别", "不同", "差异", "比较", "对比", "什么关系"
        ]
        
        # 优惠政策查询关键词
        self.discount_policy_keywords = [
            "优惠", "折扣", "减免", "免费", "半价", "打折", "特价", "便宜",
            "老人", "儿童", "学生", "军人", "残疾", "年龄", "政策", "规定",
            "标准", "条件", "要求", "资格", "证件", "身份证", "学生证"
        ]
        
        # 上下文修正规则
        self.context_rules = {
            "机场信息查询": {
                "condition": lambda query, history: self._detect_airport_info_query(query),
                "action": lambda query, history: "normal"
            },
            "交通工具分类查询": {
                "condition": lambda query, history: self._detect_transport_classification_query(query),
                "action": lambda query, history: "normal"
            },
            "优惠政策查询": {
                "condition": lambda query, history: self._detect_discount_policy_query(query),
                "action": lambda query, history: "normal"
            },
            "航班查询跟进": {
                "condition": lambda query, history: self._detect_flight_followup_query(query, history),
                "action": lambda query, history: "flights"
            },
            "航班号继承": {
                "condition": lambda query, history: self._contains_flight_number(query),
                "action": lambda query, history: self._get_flight_intent_from_history(history)
            },
            "车次号继承": {
                "condition": lambda query, history: self._contains_train_number(query),
                "action": lambda query, history: self._get_train_intent_from_history(history)
            },
            "价格查询继承": {
                "condition": lambda query, history: self._contains_price_query(query),
                "action": lambda query, history: self._get_price_intent_from_history(history)
            },
            "余票查询继承": {
                "condition": lambda query, history: self._contains_availability_query(query),
                "action": lambda query, history: self._get_availability_intent_from_history(history)
            },
            # 首轮仅有“城市到城市”表达，且未出现航班/机票关键词，则默认偏向火车
            "首轮城市到城市默认火车": {
                "condition": lambda query, history: (
                    isinstance(history, list) and len(history) <= 1 and
                    any(sep in (query or "") for sep in ["到", "-", "—", "至", "去"]) and
                    (not any(k in (query or "") for k in ["航班", "机票", "飞机"]))
                ),
                "action": lambda query, history: "train"
            }
        }
    
    def _detect_airport_info_query(self, query: str) -> bool:
        """检测是否为机场信息查询"""
        # 检查是否包含机场相关关键词
        airport_related_keywords = ["机场", "航站楼", "候机楼", "T1", "T2", "T3", "T4"]
        has_airport_keyword = any(keyword in query for keyword in airport_related_keywords)
        
        if not has_airport_keyword:
            return False
        
        # 检查是否包含信息查询关键词
        has_info_keyword = any(keyword in query for keyword in self.airport_info_keywords)
        if has_info_keyword:
            return True
        
        # 检查是否匹配机场信息查询模式
        for pattern in self.airport_info_patterns:
            if re.search(pattern, query):
                return True
        
        # 检查是否包含机场服务相关关键词
        service_keywords = ["登机", "中转", "托运", "行李", "安检", "值机", "候机", "办理", "手续"]
        has_service_keyword = any(keyword in query for keyword in service_keywords)
        
        return has_airport_keyword and has_service_keyword
    
    def _detect_flight_info_query(self, query: str) -> bool:
        """检测航班信息查询"""
        try:
            # 检测航班号 - 使用更新后的航班号检测方法
            has_flight_number = self._contains_flight_number(query)
            
            # 检测信息查询关键词
            has_info_keyword = any(keyword in query for keyword in self.flight_info_keywords)
            
            return has_flight_number and has_info_keyword
        except Exception as e:
            return False
    
    def _detect_train_info_query(self, query: str) -> bool:
        """检测车次信息查询"""
        try:
            # 检测车次号
            train_pattern = r'[GDC]\d{1,4}'
            has_train_number = bool(re.search(train_pattern, query.upper()))
            
            # 检测信息查询关键词
            has_info_keyword = any(keyword in query for keyword in self.train_info_keywords)
            
            return has_train_number and has_info_keyword
        except Exception as e:
            return False
    
    def _detect_transport_classification_query(self, query: str) -> bool:
        """检测交通工具分类查询"""
        try:
            # 检查是否包含交通工具关键词
            transport_keywords = ["高铁", "火车", "动车", "列车", "飞机", "航班", "机票", "车票"]
            has_transport_keyword = any(keyword in query for keyword in transport_keywords)
            
            # 检查是否包含分类查询关键词
            has_classification_keyword = any(keyword in query for keyword in self.transport_classification_keywords)
            
            # 特殊模式：高铁属于火车吗
            classification_patterns = [
                r"高铁.*属于.*火车",
                r"高铁.*是.*火车",
                r"高铁.*算.*火车",
                r"动车.*属于.*火车",
                r"动车.*是.*火车",
                r".*属于.*交通工具",
                r".*是什么.*交通工具"
            ]
            
            has_classification_pattern = any(re.search(pattern, query) for pattern in classification_patterns)
            
            return has_transport_keyword and (has_classification_keyword or has_classification_pattern)
        except Exception as e:
            return False
    
    def _detect_discount_policy_query(self, query: str) -> bool:
        """检测优惠政策查询"""
        try:
            # 检查是否包含交通工具关键词
            transport_keywords = ["高铁", "火车", "动车", "列车", "飞机", "航班", "机票", "车票"]
            has_transport_keyword = any(keyword in query for keyword in transport_keywords)
            
            # 检查是否包含优惠政策关键词
            has_discount_keyword = any(keyword in query for keyword in self.discount_policy_keywords)
            
            # 特殊模式：老人/儿童/学生等优惠政策查询
            discount_patterns = [
                r".*岁.*优惠",
                r".*岁.*折扣",
                r".*岁.*免费",
                r".*岁.*半价",
                r"老人.*优惠",
                r"儿童.*优惠",
                r"学生.*优惠",
                r"军人.*优惠",
                r"优惠.*年龄",
                r"优惠.*起点",
                r"优惠.*政策",
                r"票价.*优惠",
                r"购.*票.*优惠"
            ]
            
            has_discount_pattern = any(re.search(pattern, query) for pattern in discount_patterns)
            
            return has_transport_keyword and (has_discount_keyword or has_discount_pattern)
        except Exception as e:
            return False
    
    def _detect_flight_followup_query(self, query: str, history: List[Dict]) -> bool:
        """检测航班查询跟进"""
        try:
            # 检查当前查询是否是时间相关的跟进
            time_followup_keywords = ["明天", "后天", "今天", "的"]
            has_time_followup = any(keyword in query for keyword in time_followup_keywords)
            
            if not has_time_followup:
                return False
            
            # 检查历史中是否有航班号
            for msg in reversed(history):
                content = msg.get('content', '')
                
                # 检查是否包含航班号 - 使用更新后的航班号检测方法
                if self._contains_flight_number(content):
                    return True
                # 检查是否包含航班相关关键词
                if any(keyword in content for keyword in ["航班", "机票", "飞机", "餐食", "餐"]):
                    return True
            
            return False
        except Exception as e:
            return False
    
    def _extract_flight_number(self, query: str) -> str:
        """从查询中提取航班号"""
        try:
            # 支持多种航班号格式，兼容中文环境：
            # 1. 标准格式：两个字母+数字 (如 CA1234, MU5678, JD5128)
            # 2. 数字开头格式：数字+字母+数字 (如 9C9980, 3U8925)
            flight_patterns = [
                r'[A-Z]{2}\d{3,4}',      # 标准格式：CA1234, MU5678, JD5128
                r'\d[A-Z]\d{3,4}',       # 数字开头：9C9980, 3U8925
                r'[A-Z]\d[A-Z]\d{3,4}',  # 字母数字字母：如果有这种格式
            ]
            
            for pattern in flight_patterns:
                match = re.search(pattern, query.upper())
                if match:
                    return match.group(0)
            return ""
        except Exception:
            return ""
    
    def _extract_train_number(self, query: str) -> str:
        """从查询中提取车次号"""
        try:
            # 提取车次号，但要避免误匹配航班号和航站楼标识
            train_pattern = r'[GDTCKZXYL]\d{1,4}(?![A-Z0-9])'
            matches = re.findall(train_pattern, query.upper())
            
            # 进一步过滤：排除航班号的一部分和航站楼标识
            for match in matches:
                if self._is_part_of_flight_number(match, query.upper()):
                    continue
                    
                if self._is_terminal_identifier(match, query):
                    continue
                    
                return match
            return ""
        except Exception:
            return ""
    
    def _contains_flight_number(self, query: str) -> bool:
        """检查查询是否包含航班号"""
        # 支持多种航班号格式，兼容中文环境和大小写
        flight_patterns = [
            r'[A-Z]{2}\d{3,4}',      # 标准格式：CA1234, MU5678, JD5128
            r'\d[A-Z]\d{3,4}',       # 数字开头：9C9980, 3U8925
            r'[A-Z]\d[A-Z]\d{3,4}',  # 字母数字字母：如果有这种格式
        ]
        
        for pattern in flight_patterns:
            if re.search(pattern, query.upper()):
                return True
        return False
    
    def _contains_train_number(self, query: str) -> bool:
        """检查查询是否包含车次号"""
        # 根据Java代码规则：G、D、T、K、C、Z、X、Y、L开头的车次号
        # 但要避免误匹配航班号和航站楼标识
        train_pattern = r'[GDTCKZXYL]\d{1,4}(?![A-Z0-9])'  # 负向前瞻，确保后面不跟字母或数字
        matches = re.findall(train_pattern, query.upper())
        
        # 进一步过滤：排除航班号的一部分和航站楼标识
        for match in matches:
            # 检查这个匹配是否是航班号的一部分
            if self._is_part_of_flight_number(match, query.upper()):
                continue
            
            # 检查是否是航站楼标识（如T1, T2, T3等）
            if self._is_terminal_identifier(match, query):
                continue
                
            return True
        return False
    
    def _contains_price_query(self, query: str) -> bool:
        """检查查询是否包含价格查询"""
        price_keywords = ["多少钱", "价格", "费用", "票价", "票价多少"]
        return any(keyword in query for keyword in price_keywords)
    
    def _contains_availability_query(self, query: str) -> bool:
        """检查查询是否包含余票查询"""
        availability_keywords = ["余票", "还有票", "座位", "位置", "有票吗"]
        return any(keyword in query for keyword in availability_keywords)
    
    def _get_flight_intent_from_history(self, history: List[Dict]) -> Optional[str]:
        """从历史中获取航班相关意图"""
        for msg in reversed(history):
            if msg.get('intent') in ['查询航班', '预订航班', '查询机票', '机票价格', '机票余票']:
                return msg.get('intent')
        return None
    
    def _get_train_intent_from_history(self, history: List[Dict]) -> Optional[str]:
        """从历史中获取火车相关意图"""
        for msg in reversed(history):
            if msg.get('intent') in ['查询火车', '预订火车', '查询车票', '车票价格', '车票余票']:
                return msg.get('intent')
        return None
    
    def _get_price_intent_from_history(self, history: List[Dict]) -> Optional[str]:
        """从历史中获取价格查询意图"""
        # 优先从最近一条往回看，根据文本内容判断是航班还是火车上下文
        try:
            import re
        except Exception:
            re = None

        for msg in reversed(history):
            text = (msg.get('content') or '').upper()

            # 航班号 / 车次号信号
            if re:
                if re.search(r"\b[A-Z]{2}\d{3,4}\b", text):
                    return 'flights'
                if re.search(r"\b[GDTCZKXYL]\d{1,4}\b", text):
                    return 'train'

            # 关键词信号
            if any(k in text for k in ['航班', '机票', '飞机', 'FLIGHT']):
                return 'flights'
            if any(k in text for k in ['火车', '高铁', '动车', '列车', '车票', 'TRAIN']):
                return 'train'

        return None
    
    def _get_availability_intent_from_history(self, history: List[Dict]) -> Optional[str]:
        """从历史中获取余票查询意图"""
        for msg in reversed(history):
            if msg.get('intent') in ['机票余票', '车票余票', '查询余票', '余票查询']:
                return msg.get('intent')
        return None
    
    def _is_part_of_flight_number(self, train_match: str, query: str) -> bool:
        """检查车次号匹配是否是航班号的一部分"""
        # 检查是否存在完整的航班号包含这个车次号匹配
        flight_patterns = [
            r'[A-Z]{2}\d{3,4}',      # 标准格式：CA1234, MU5678, JD5128
            r'\d[A-Z]\d{3,4}',       # 数字开头：9C9980, 3U8925
            r'[A-Z]\d[A-Z]\d{3,4}',  # 字母数字字母：如果有这种格式
        ]
        
        for pattern in flight_patterns:
            flight_matches = re.findall(pattern, query)
            for flight_match in flight_matches:
                if train_match in flight_match:
                    return True
        return False
    
    def _is_terminal_identifier(self, match: str, query: str) -> bool:
        """检查匹配是否是航站楼标识"""
        # 检查是否是航站楼标识（如T1, T2, T3等）
        if match.startswith('T') and len(match) <= 3:
            # 检查上下文是否包含航站楼相关关键词
            terminal_keywords = [
                "航站楼", "候机楼", "机场", "登机", "中转", "托运", "行李", 
                "安检", "值机", "候机", "出发", "到达", "航班"
            ]
            
            for keyword in terminal_keywords:
                if keyword in query:
                    return True
        
        return False
    
    def keyword_matching(self, query: str) -> RuleResult:
        """基于语义模式的意图识别，而不是简单关键词匹配"""
        # 首先检查是否为明确的预订/查询意图
        booking_intent = self._detect_booking_intent(query)
        if booking_intent:
            return booking_intent
        
        # 检查是否为信息咨询意图
        info_intent = self._detect_info_query_intent(query)
        if info_intent:
            return info_intent
        
        # 如果都不是，回退到传统关键词匹配（但降低置信度）
        return self._fallback_keyword_matching(query)
    
    def _detect_booking_intent(self, query: str) -> Optional[RuleResult]:
        """检测预订/查询意图"""
        # 预订相关的动作词
        booking_actions = ["买", "订", "预订", "预约", "购买", "查询", "搜索", "找", "要", "需要"]
        # 具体的交通工具+票据组合
        transport_tickets = ["机票", "飞机票", "车票", "火车票", "高铁票", "动车票"]
        # 路线模式：A到B、A-B等
        route_patterns = [
            r"[\u4e00-\u9fff]+到[\u4e00-\u9fff]+",  # 中文地名到中文地名
            r"[\u4e00-\u9fff]+-[\u4e00-\u9fff]+",   # 中文地名-中文地名
            r"[\u4e00-\u9fff]+至[\u4e00-\u9fff]+",  # 中文地名至中文地名
        ]
        
        # 检查是否包含预订动作
        has_booking_action = any(action in query for action in booking_actions)
        
        # 检查是否包含具体票据
        has_ticket = any(ticket in query for ticket in transport_tickets)
        
        # 检查是否包含路线信息
        has_route = any(re.search(pattern, query) for pattern in route_patterns)
        
        # 检查是否包含航班号/车次号
        has_flight_number = self._contains_flight_number(query)
        has_train_number = self._contains_train_number(query)
        
        # 判断意图
        if has_booking_action and (has_ticket or has_route or has_flight_number or has_train_number):
            if has_ticket and ("机票" in query or "飞机票" in query) or has_flight_number:
                return RuleResult(intent="flights", confidence=0.9, matched_patterns=["预订查询-航班"])
            elif has_ticket and ("车票" in query or "火车票" in query or "高铁票" in query or "动车票" in query) or has_train_number:
                return RuleResult(intent="train", confidence=0.9, matched_patterns=["预订查询-火车"])
        
        # 仅有路线信息，默认偏向火车（根据历史数据）
        if has_route and not has_flight_number and not any(word in query for word in ["飞", "航班", "机票"]):
            return RuleResult(intent="train", confidence=0.7, matched_patterns=["路线查询-默认火车"])
        
        return None
    
    def _detect_info_query_intent(self, query: str) -> Optional[RuleResult]:
        """检测信息咨询意图"""
        # 咨询相关的疑问词和句式
        question_words = ["什么", "怎么", "如何", "为什么", "哪些", "多少", "几", "是否", "有没有", "能不能", "可以吗"]
        question_patterns = [
            r".*吗\？?$",  # 以"吗"结尾的疑问句
            r".*呢\？?$",  # 以"呢"结尾的疑问句
            r".*\？$",     # 以问号结尾
        ]
        
        # 政策/规则相关词汇
        policy_words = ["政策", "规定", "规则", "要求", "条件", "标准", "流程", "手续", "证件", "优惠", "折扣", "免费", "服务", "安排", "提供"]
        
        # 检查是否为疑问句
        has_question_word = any(word in query for word in question_words)
        has_question_pattern = any(re.search(pattern, query) for pattern in question_patterns)
        is_question = has_question_word or has_question_pattern
        
        # 检查是否包含政策相关词汇
        has_policy_word = any(word in query for word in policy_words)
        
        # 如果是疑问句且包含政策词汇，很可能是信息咨询
        if is_question and has_policy_word:
            return RuleResult(intent="normal", confidence=0.9, matched_patterns=["信息咨询"])
        
        return None
    
    def _fallback_keyword_matching(self, query: str) -> RuleResult:
        """传统关键词匹配作为后备方案，但降低置信度"""
        matched_keywords = []
        matched_intent = None
        max_matches = 0
        max_confidence = 0.0
        
        # 对查询进行分词
        words = list(jieba.cut(query))
        
        for intent, keywords in self.keyword_rules.items():
            matches = 0
            intent_keywords = []
            
            for keyword in keywords:
                if keyword in query:
                    matches += 1
                    intent_keywords.append(keyword)
            
            # 计算置信度：降低基础置信度，避免误判
            confidence = 0.0
            if matches > 0:
                # 基础匹配分数（降低）
                base_score = (matches / len(words)) * 0.5 if words else 0.0
                
                # 长关键词奖励分数
                long_keyword_bonus = 0.0
                for keyword in intent_keywords:
                    if len(keyword) >= 4:  # 4个字符以上的关键词
                        long_keyword_bonus += 0.1
                
                confidence = min(base_score + long_keyword_bonus, 0.6)  # 最高置信度限制为0.6
            
            if confidence > max_confidence:
                max_confidence = confidence
                max_matches = matches
                matched_intent = intent
                matched_keywords = intent_keywords
        
        return RuleResult(
            intent=matched_intent,
            confidence=max_confidence,
            matched_keywords=matched_keywords
        )
    
    def pattern_matching(self, query: str) -> RuleResult:
        """模板级规则匹配"""
        matched_patterns = []
        matched_intent = None
        max_confidence = 0.0
        
        for intent, patterns in self.pattern_rules.items():
            for pattern in patterns:
                if re.search(pattern, query):
                    matched_intent = intent
                    matched_patterns.append(pattern)
                    
                    # 长模式匹配给予更高置信度
                    pattern_length = len(pattern.replace(r".*", ""))
                    confidence = min(0.8 + (pattern_length * 0.02), 1.0)
                    
                    if confidence > max_confidence:
                        max_confidence = confidence
                    break
            if matched_intent:
                break
        
        return RuleResult(
            intent=matched_intent,
            confidence=max_confidence,
            matched_patterns=matched_patterns
        )
    
    def context_correction(self, query: str, history: List[Dict]) -> RuleResult:
        """上下文修正规则"""
        for rule_name, rule in self.context_rules.items():
            if rule['condition'](query, history):
                intent = rule['action'](query, history)
                if intent:
                    # 机场信息查询给予最高优先级
                    confidence = 1.0 if rule_name == "机场信息查询" else 0.9
                    return RuleResult(
                        intent=intent,
                        confidence=confidence,
                        matched_patterns=[rule_name]
                    )
        
        return RuleResult(intent=None, confidence=0.0)
    
    def apply_rules(self, query: str, history: List[Dict] = None) -> RuleResult:
        """应用所有规则"""
        if history is None:
            history = []
        
        # 优先级1: 航班信息查询检查
        if self._detect_flight_info_query(query):
            flight_number = self._extract_flight_number(query)
            return RuleResult(
                intent="flights",
                confidence=1.0,
                matched_patterns=["航班信息查询"],
                extracted_number=flight_number
            )
        
        # 优先级2: 车次信息查询检查  
        if self._detect_train_info_query(query):
            train_number = self._extract_train_number(query)
            return RuleResult(
                intent="train",
                confidence=1.0,
                matched_patterns=["车次信息查询"],
                extracted_number=train_number
            )
        
        # 优先级3: 中转服务咨询检查（优先级高于机场信息查询）
        transit_patterns = [
            r".*中转.*停留.*",
            r".*机场.*停留.*小时.*",
            r".*航司.*安排.*",
            r".*停留.*小时.*机场.*",
            r".*停留.*小时.*航司.*"
        ]
        
        if any(re.search(pattern, query) for pattern in transit_patterns):
            return RuleResult(
                intent="normal",
                confidence=1.0,
                matched_patterns=["中转服务咨询"]
            )
        
        # 优先级4: 机场信息查询检查
        if self._detect_airport_info_query(query):
            return RuleResult(
                intent="normal",
                confidence=1.0,
                matched_patterns=["机场信息查询"]
            )
        
        # 优先级5: 优惠政策查询检查
        if self._detect_discount_policy_query(query):
            return RuleResult(
                intent="normal",
                confidence=1.0,
                matched_patterns=["优惠政策查询"]
            )
        
        # 优先级6: 交通工具分类查询检查
        if self._detect_transport_classification_query(query):
            return RuleResult(
                intent="normal",
                confidence=1.0,
                matched_patterns=["交通工具分类查询"]
            )
        
        # 优先级7: 航班查询跟进检查
        if self._detect_flight_followup_query(query, history):
            return RuleResult(
                intent="flights",
                confidence=1.0,
                matched_patterns=["航班查询跟进"]
            )
        
        # 1. 关键词匹配
        keyword_result = self.keyword_matching(query)
        
        # 2. 模式匹配
        pattern_result = self.pattern_matching(query)
        
        # 3. 上下文修正
        context_result = self.context_correction(query, history)
        
        # 选择置信度最高的结果
        results = [keyword_result, pattern_result, context_result]
        best_result = max(results, key=lambda x: x.confidence)
        
        return best_result
    
    def add_keyword_rule(self, intent: str, keywords: List[str]):
        """添加关键词规则"""
        if intent not in self.keyword_rules:
            self.keyword_rules[intent] = []
        self.keyword_rules[intent].extend(keywords)
    
    def add_pattern_rule(self, intent: str, patterns: List[str]):
        """添加模式规则"""
        if intent not in self.pattern_rules:
            self.pattern_rules[intent] = []
        self.pattern_rules[intent].extend(patterns)
    
    def add_context_rule(self, rule_name: str, condition_func, action_func):
        """添加上下文规则"""
        self.context_rules[rule_name] = {
            "condition": condition_func,
            "action": action_func
        } 
