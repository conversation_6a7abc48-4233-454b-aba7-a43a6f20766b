#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IntentFix 服务启动脚本
启动Web API服务
"""

import uvicorn
import argparse
import os
import sys
from config.settings import settings

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="IntentFix Web API 服务启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python server.py                    # 默认启动（端口8000）
  python server.py --port 8080       # 指定端口
  python server.py --host 0.0.0.0    # 指定主机
  python server.py --reload           # 开发模式（自动重载）
  python server.py --workers 4        # 生产模式（多进程）
        """
    )
    
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="服务主机地址（默认：0.0.0.0）"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8100,
        help="服务端口（默认：8000）"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="开发模式，自动重载代码"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="工作进程数（默认：1）"
    )
    
    parser.add_argument(
        "--log-level",
        default=settings.LOG_LEVEL.lower(),
        choices=["debug", "info", "warning", "error"],
        help="日志级别（默认：INFO）"
    )
    
    args = parser.parse_args()
    
    # 检查配置
    if not settings.validate_config():
        print("❌ 配置验证失败，请检查配置文件")
        sys.exit(1)
    
    print("🚀 启动 IntentFix Web API 服务")
    print(f"📋 版本: {settings.VERSION}")
    print(f"🌐 地址: http://{args.host}:{args.port}")
    print(f"📚 API文档: http://{args.host}:{args.port}/docs")
    print(f"🔍 健康检查: http://{args.host}:{args.port}/health")
    print(f"⚙️  模式: {'开发模式' if args.reload else '生产模式'}")
    print(f"👥 进程数: {args.workers}")
    print(f"📝 日志级别: {args.log_level.upper()}")
    print("=" * 50)
    
    # 启动服务
    uvicorn.run(
        "api.app:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers if not args.reload else 1,
        log_level=args.log_level,
        access_log=True
    )

if __name__ == "__main__":
    main() 