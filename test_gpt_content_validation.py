#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GPT内容校验功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.models import GptContent, IntentFixInput, IntentFixOutput, SlotInfo, UserMessage
from core.intent_fix import IntentFixEngine
from datetime import datetime


def test_gpt_content_validation():
    """测试GPT内容校验功能"""
    
    # 初始化意图修正引擎
    engine = IntentFixEngine()
    
    # 测试用例1：正常的GPT内容
    test_case_1 = {
        "gptcontent": {
            "intent": "flights",
            "mainBusinessType": "机票",
            "org": "",
            "dst": "北京",
            "departDate": "2025-07-30",
            "returnDate": "",
            "flightTrainNo": "CA1234",
            "questions": [],
            "itinerary_num": "",
            "airport": ""
        },
        "slot": {
            "intent": "flights",
            "departDate": "2025-07-30",
            "mainBusinessType": "机票"
        },
        "userHistory": [
            {
                "role": "user",
                "time": datetime.now(),
                "type": "text",
                "content": "从上海到北京的CA1234航班"
            }
        ],
        "domain": "flight"
    }
    
    # 测试用例2：包含无效数据的GPT内容
    test_case_2 = {
        "gptcontent": {
            "intent": "train",
            "mainBusinessType": "火车票",
            "org": "上海",
            "dst": "北京",
            "departDate": "明天",
            "returnDate": "",
            "flightTrainNo": "G1234",
            "questions": [],
            "itinerary_num": "",
            "airport": ""
        },
        "slot": {
            "intent": "train",
            "departDate": "明天",
            "mainBusinessType": "火车票"
        },
        "userHistory": [
            {
                "role": "user",
                "time": datetime.now(),
                "type": "text",
                "content": "明天从上海到北京的高铁G1234"
            }
        ],
        "domain": "train"
    }
    
    # 测试用例3：包含冲突的GPT内容
    test_case_3 = {
        "gptcontent": {
            "intent": "flights",
            "mainBusinessType": "机票",
            "org": "广州",
            "dst": "深圳",
            "departDate": "2025-07-30",
            "returnDate": "",
            "flightTrainNo": "MU5678",
            "questions": [],
            "itinerary_num": "",
            "airport": ""
        },
        "slot": {
            "intent": "flights",
            "departDate": "2025-07-30",
            "mainBusinessType": "机票"
        },
        "userHistory": [
            {
                "role": "user",
                "time": datetime.now(),
                "type": "text",
                "content": "从广州到深圳的MU5678航班"
            },
            {
                "role": "user",
                "time": datetime.now(),
                "type": "text",
                "content": "不对，应该是从北京到上海"
            }
        ],
        "domain": "flight"
    }
    
    test_cases = [
        ("正常GPT内容", test_case_1),
        ("包含无效数据", test_case_2),
        ("包含冲突数据", test_case_3)
    ]
    
    print("=" * 60)
    print("GPT内容校验测试")
    print("=" * 60)
    
    for test_name, test_case in test_cases:
        print(f"\n测试: {test_name}")
        print("-" * 40)
        
        # 构建输入数据
        input_data = IntentFixInput(
            gptcontent=GptContent(**test_case["gptcontent"]),
            slot=SlotInfo(**test_case["slot"]),
            userHistory=[UserMessage(**msg) for msg in test_case["userHistory"]],
            domain=test_case["domain"]
        )
        
        try:
            # 执行意图修正
            result = engine.fix_intent(input_data)
            
            print(f"输入意图: {input_data.gptcontent.intent}")
            print(f"输出意图: {result.gptcontent.intent}")
            print(f"主业务类型: {result.gptcontent.mainBusinessType}")
            print(f"出发地: {result.gptcontent.org}")
            print(f"目的地: {result.gptcontent.dst}")
            print(f"出发日期: {result.gptcontent.departDate}")
            print(f"返回日期: {result.gptcontent.returnDate}")
            print(f"航班/车次号: {result.gptcontent.flightTrainNo}")
            
            if result.resultReply:
                print(f"澄清消息: {result.resultReply}")
            
        except Exception as e:
            print(f"测试失败: {e}")


def test_parameter_extraction():
    """测试参数提取功能"""
    
    from core.parameter_validator import ParameterValidator
    
    validator = ParameterValidator()
    
    test_queries = [
        "明天从北京到上海的CA1234航班",
        "后天从广州到深圳的高铁G1234",
        "今天从成都到重庆的动车D123",
        "从杭州到南京的航班MU5678",
        "查询明天从武汉到西安的火车K123"
    ]
    
    print("\n" + "=" * 60)
    print("参数提取测试")
    print("=" * 60)
    
    for query in test_queries:
        print(f"\n查询: {query}")
        print("-" * 30)
        
        # 创建空的GPT内容
        gpt_content = GptContent(
            intent="",
            org="",
            dst="",
            departDate="",
            returnDate="",
            flightTrainNo="",
            questions=[],
            itinerary_num="",
            airport=""
        )
        
        # 从查询中提取信息
        updated_content = validator._extract_from_query(gpt_content, query)
        
        print(f"提取的出发地: {updated_content.org}")
        print(f"提取的目的地: {updated_content.dst}")
        print(f"提取的出发日期: {updated_content.departDate}")
        print(f"提取的返回日期: {updated_content.returnDate}")
        print(f"提取的航班/车次号: {updated_content.flightTrainNo}")


if __name__ == "__main__":
    test_gpt_content_validation()
    test_parameter_extraction()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60) 