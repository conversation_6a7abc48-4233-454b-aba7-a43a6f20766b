#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试兼容字段功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.models import GptContent, IntentFixInput, IntentFixOutput, SlotInfo, UserMessage
from core.intent_fix import IntentFixEngine
from datetime import datetime


def test_compatible_fields():
    """测试兼容字段功能"""
    
    # 初始化意图修正引擎
    engine = IntentFixEngine()
    
    # 测试用例：使用实际数据格式
    test_case = {
        "gptcontent": {
            "intent": "flight",
            "航班车次号": "CA1234",
            "出发日期": "2025-01-15",
            "dst": "上海",
            "主业务类型": "机票"
        },
        "slot": {
            "intent": "train",
            "航班车次号": "G11",
            "出发日期": "2025-01-15",
            "返回日期": "2025-01-18",
            "org": "北京",
            "dst": "上海",
            "主业务类型": "火车票"
        },
        "userHistory": [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "CA1234航班什么时候起飞？"
            },
            {
                "role": "system",
                "time": "2025-01-15T10:30:05",
                "type": "text",
                "content": "正在查询CA1234航班信息..."
            }
        ],
        "domain": "tmall"
    }
    
    print("=" * 60)
    print("兼容字段测试")
    print("=" * 60)
    
    print(f"\n输入数据:")
    print(f"GPT内容: {test_case['gptcontent']}")
    print(f"Slot信息: {test_case['slot']}")
    
    # 构建输入数据
    input_data = IntentFixInput(
        gptcontent=GptContent(**test_case["gptcontent"]),
        slot=SlotInfo(**test_case["slot"]),
        userHistory=[UserMessage(**msg) for msg in test_case["userHistory"]],
        domain=test_case["domain"]
    )
    
    try:
        # 执行意图修正
        result = engine.fix_intent(input_data)
        
        print(f"\n输出结果:")
        print(f"意图: {result.gptcontent.intent}")
        print(f"主业务类型: {result.gptcontent.mainBusinessType}")
        print(f"出发地: {result.gptcontent.org}")
        print(f"目的地: {result.gptcontent.dst}")
        print(f"出发日期: {result.gptcontent.departDate}")
        print(f"返回日期: {result.gptcontent.returnDate}")
        print(f"航班/车次号: {result.gptcontent.flightTrainNo}")
        
        # 检查兼容字段
        print(f"\n兼容字段:")
        print(f"出发日期: {result.gptcontent.出发日期}")
        print(f"返回日期: {result.gptcontent.返回日期}")
        print(f"航班车次号: {result.gptcontent.航班车次号}")
        print(f"主业务类型: {result.gptcontent.主业务类型}")
        
        if result.resultReply:
            print(f"\n澄清消息: {result.resultReply}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_mixed_fields():
    """测试混合字段格式"""
    
    engine = IntentFixEngine()
    
    # 测试用例：混合使用新旧字段名
    test_case = {
        "gptcontent": {
            "intent": "flights",
            "flightTrainNo": "MU5678",
            "departDate": "2025-01-20",
            "dst": "北京",
            "mainBusinessType": "机票",
            "returnDate": "2025-01-25"
        },
        "slot": {
            "intent": "flights",
            "departDate": "2025-01-20",
            "mainBusinessType": "机票"
        },
        "userHistory": [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "MU5678航班从上海到北京"
            }
        ],
        "domain": "flight"
    }
    
    print("\n" + "=" * 60)
    print("混合字段测试")
    print("=" * 60)
    
    input_data = IntentFixInput(
        gptcontent=GptContent(**test_case["gptcontent"]),
        slot=SlotInfo(**test_case["slot"]),
        userHistory=[UserMessage(**msg) for msg in test_case["userHistory"]],
        domain=test_case["domain"]
    )
    
    try:
        result = engine.fix_intent(input_data)
        
        print(f"\n输出结果:")
        print(f"意图: {result.gptcontent.intent}")
        print(f"主业务类型: {result.gptcontent.mainBusinessType}")
        print(f"出发地: {result.gptcontent.org}")
        print(f"目的地: {result.gptcontent.dst}")
        print(f"出发日期: {result.gptcontent.departDate}")
        print(f"返回日期: {result.gptcontent.returnDate}")
        print(f"航班/车次号: {result.gptcontent.flightTrainNo}")
        
        # 检查兼容字段
        print(f"\n兼容字段:")
        print(f"出发日期: {result.gptcontent.出发日期}")
        print(f"返回日期: {result.gptcontent.返回日期}")
        print(f"航班车次号: {result.gptcontent.航班车次号}")
        print(f"主业务类型: {result.gptcontent.主业务类型}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_compatible_fields()
    test_mixed_fields()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60) 