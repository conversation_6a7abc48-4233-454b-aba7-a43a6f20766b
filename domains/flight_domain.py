from typing import Dict, List, Optional
from core.intent_fix import IntentFixEngine
from core.models import IntentFixInput, IntentFixOutput


class FlightDomainEngine(IntentFixEngine):
    """航班域意图修正引擎"""
    
    def __init__(self):
        super().__init__(domain="flight")
        self._setup_flight_specific_rules()
    
    def _setup_flight_specific_rules(self):
        """设置航班域特定规则"""
        
        # 航班域关键词规则
        flight_keyword_rules = {
            "查询航班": [
                "查询", "查找", "搜索", "找", "查", "航班", "飞机",
                "什么时候", "几点", "时间", "时刻表", "航班信息",
                "CA", "MU", "航班号", "什么时候起飞", "什么时候到达"
            ],
            "预订航班": [
                "预订", "预约", "订票", "买票", "购买", "订", "预约",
                "要订", "想订", "帮我订", "帮我预约", "订一张票",
                "买一张票", "预订机票", "预约机票"
            ],
            "改签": [
                "改签", "改票", "换票", "改航班", "换航班", "改期",
                "改时间", "换时间", "改日期", "换日期"
            ],
            "退票": [
                "退票", "退款", "取消", "不要了", "不订了",
                "取消订单", "退订", "撤销"
            ],
            "查询天气": [
                "天气", "气温", "温度", "下雨", "晴天", "阴天",
                "天气预报", "今天天气", "明天天气", "目的地天气"
            ]
        }
        
        # 航班域模式规则
        flight_pattern_rules = {
            "查询航班": [
                r".*查询.*航班.*",
                r".*CA\d{3,4}.*",
                r".*MU\d{3,4}.*",
                r".*什么时候.*",
                r".*几点.*",
                r".*时间.*"
            ],
            "预订航班": [
                r".*预订.*航班.*",
                r".*订票.*",
                r".*买票.*",
                r".*预约.*"
            ],
            "改签": [
                r".*改签.*",
                r".*改票.*",
                r".*换票.*",
                r".*改.*航班.*"
            ],
            "退票": [
                r".*退票.*",
                r".*取消.*订单.*",
                r".*不要了.*"
            ]
        }
        
        # 添加航班域特定规则
        self.add_custom_rules(
            domain="flight",
            keyword_rules=flight_keyword_rules,
            pattern_rules=flight_pattern_rules
        )
    
    def fix_intent(self, input_data: IntentFixInput) -> IntentFixOutput:
        """航班域意图修正"""
        
        # 调用父类方法进行基础修正
        result = super().fix_intent(input_data)
        
        # 航班域特定处理
        result = self._apply_flight_specific_logic(result, input_data)
        
        return result
    
    def \
            _apply_flight_specific_logic(self, result: IntentFixOutput, input_data: IntentFixInput) -> IntentFixOutput:
        """应用航班域特定逻辑"""
        
        # 1. 航班号特殊处理
        if result.gptcontent.flightNo:
            # 确保航班号格式正确
            flight_no = result.gptcontent.flightNo.upper()
            if len(flight_no) >= 5 and flight_no[:2].isalpha() and flight_no[2:].isdigit():
                result.gptcontent.flightNo = flight_no
        
        # 2. 日期格式标准化
        if result.gptcontent.departDate:
            # 确保日期格式为 YYYY-MM-DD
            try:
                from datetime import datetime
                date_obj = datetime.strptime(result.gptcontent.departDate, "%Y-%m-%d")
                result.gptcontent.departDate = date_obj.strftime("%Y-%m-%d")
            except:
                pass
        
        # 3. 城市名称标准化
        city_mapping = {
            "北京": "北京",
            "上海": "上海",
            "广州": "广州",
            "深圳": "深圳",
            "杭州": "杭州",
            "南京": "南京",
            "成都": "成都",
            "武汉": "武汉",
            "西安": "西安",
            "重庆": "重庆"
        }
        
        if result.gptcontent.departCity:
            for original, standard in city_mapping.items():
                if original in result.gptcontent.departCity:
                    result.gptcontent.departCity = standard
                    break
        
        if result.gptcontent.arriveCity:
            for original, standard in city_mapping.items():
                if original in result.gptcontent.arriveCity:
                    result.gptcontent.arriveCity = standard
                    break
        
        # 4. 意图特定处理
        # 无“延误登机”专用逻辑
        
        elif result.gptcontent.intent == "预订航班":
            # 预订航班需要基本信息
            missing_info = []
            if not result.gptcontent.departCity:
                missing_info.append("出发城市")
            if not result.gptcontent.arriveCity:
                missing_info.append("到达城市")
            if not result.gptcontent.departDate:
                missing_info.append("出发日期")
            
            if missing_info:
                result.resultReply = f"预订航班需要提供：{', '.join(missing_info)}"
        
        elif result.gptcontent.intent == "查询航班":
            # 查询航班需要航班号或基本信息
            if not result.gptcontent.flightNo and not result.gptcontent.departCity:
                result.resultReply = "请提供航班号或出发城市信息"
        
        return result
    
    def validate_flight_specific_parameters(self, slot_info: Dict) -> Dict:
        """验证航班域特定参数"""
        validated = slot_info.copy()
        
        # 验证航班号格式
        if 'flightNo' in validated and validated['flightNo']:
            flight_no = validated['flightNo'].upper()
            if len(flight_no) >= 5 and flight_no[:2].isalpha() and flight_no[2:].isdigit():
                validated['flightNo'] = flight_no
            else:
                validated['flightNo'] = None
        
        # 验证日期格式
        date_fields = ['departDate', 'arriveDate']
        for field in date_fields:
            if field in validated and validated[field]:
                try:
                    from datetime import datetime
                    date_obj = datetime.strptime(validated[field], "%Y-%m-%d")
                    validated[field] = date_obj.strftime("%Y-%m-%d")
                except:
                    validated[field] = None
        
        return validated 