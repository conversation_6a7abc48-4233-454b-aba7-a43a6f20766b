from typing import Dict, List, Optional
from core.intent_fix import IntentFixEngine
from core.models import IntentFixInput, IntentFixOutput


class TrainDomainEngine(IntentFixEngine):
    """列车域意图修正引擎"""
    
    def __init__(self):
        super().__init__(domain="train")
        self._setup_train_specific_rules()
    
    def _setup_train_specific_rules(self):
        """设置列车域特定规则"""
        
        # 列车域关键词规则
        train_keyword_rules = {
            "查询列车": [
                "查询", "查找", "搜索", "找", "查", "列车", "火车",
                "什么时候", "几点", "时间", "时刻表", "列车信息",
                "G", "D", "K", "T", "Z", "车次", "什么时候发车", "什么时候到达"
            ],
            "预订列车": [
                "预订", "预约", "订票", "买票", "购买", "订", "预约",
                "要订", "想订", "帮我订", "帮我预约", "订一张票",
                "买一张票", "预订车票", "预约车票"
            ],
            "改签": [
                "改签", "改票", "换票", "改列车", "换列车", "改期",
                "改时间", "换时间", "改日期", "换日期"
            ],
            "退票": [
                "退票", "退款", "取消", "不要了", "不订了",
                "取消订单", "退订", "撤销"
            ]
        }
        
        # 列车域模式规则
        train_pattern_rules = {
            "查询列车": [
                r".*查询.*列车.*",
                r".*G\d{3,4}.*",
                r".*D\d{3,4}.*",
                r".*K\d{3,4}.*",
                r".*什么时候.*",
                r".*几点.*",
                r".*时间.*"
            ],
            "预订列车": [
                r".*预订.*列车.*",
                r".*订票.*",
                r".*买票.*",
                r".*预约.*"
            ],
            "改签": [
                r".*改签.*",
                r".*改票.*",
                r".*换票.*",
                r".*改.*列车.*"
            ],
            "退票": [
                r".*退票.*",
                r".*取消.*订单.*",
                r".*不要了.*"
            ]
        }
        
        # 添加列车域特定规则
        self.add_custom_rules(
            domain="train",
            keyword_rules=train_keyword_rules,
            pattern_rules=train_pattern_rules
        )
    
    def fix_intent(self, input_data: IntentFixInput) -> IntentFixOutput:
        """列车域意图修正"""
        
        # 调用父类方法进行基础修正
        result = super().fix_intent(input_data)
        
        # 列车域特定处理
        result = self._apply_train_specific_logic(result, input_data)
        
        return result
    
    def _apply_train_specific_logic(self, result: IntentFixOutput, input_data: IntentFixInput) -> IntentFixOutput:
        """应用列车域特定逻辑"""
        
        # 1. 列车号特殊处理
        if result.gptcontent.trainNo:
            # 确保列车号格式正确
            train_no = result.gptcontent.trainNo.upper()
            if len(train_no) >= 2 and train_no[0].isalpha() and train_no[1:].isdigit():
                result.gptcontent.trainNo = train_no
        
        # 2. 日期格式标准化
        if result.gptcontent.departDate:
            # 确保日期格式为 YYYY-MM-DD
            try:
                from datetime import datetime
                date_obj = datetime.strptime(result.gptcontent.departDate, "%Y-%m-%d")
                result.gptcontent.departDate = date_obj.strftime("%Y-%m-%d")
            except:
                pass
        
        # 3. 城市名称标准化
        city_mapping = {
            "北京": "北京",
            "上海": "上海",
            "广州": "广州",
            "深圳": "深圳",
            "杭州": "杭州",
            "南京": "南京",
            "成都": "成都",
            "武汉": "武汉",
            "西安": "西安",
            "重庆": "重庆",
            "天津": "天津",
            "青岛": "青岛",
            "大连": "大连",
            "厦门": "厦门",
            "苏州": "苏州"
        }
        
        if result.gptcontent.departCity:
            for original, standard in city_mapping.items():
                if original in result.gptcontent.departCity:
                    result.gptcontent.departCity = standard
                    break
        
        if result.gptcontent.arriveCity:
            for original, standard in city_mapping.items():
                if original in result.gptcontent.arriveCity:
                    result.gptcontent.arriveCity = standard
                    break
        
        # 4. 意图特定处理
        # 无“延误登机”专用逻辑
        
        elif result.gptcontent.intent == "预订列车":
            # 预订列车需要基本信息
            missing_info = []
            if not result.gptcontent.departCity:
                missing_info.append("出发城市")
            if not result.gptcontent.arriveCity:
                missing_info.append("到达城市")
            if not result.gptcontent.departDate:
                missing_info.append("出发日期")
            
            if missing_info:
                result.resultReply = f"预订列车需要提供：{', '.join(missing_info)}"
        
        elif result.gptcontent.intent == "查询列车":
            # 查询列车需要列车号或基本信息
            if not result.gptcontent.trainNo and not result.gptcontent.departCity:
                result.resultReply = "请提供车次号或出发城市信息"
        
        return result
    
    def validate_train_specific_parameters(self, slot_info: Dict) -> Dict:
        """验证列车域特定参数"""
        validated = slot_info.copy()
        
        # 验证列车号格式
        if 'trainNo' in validated and validated['trainNo']:
            train_no = validated['trainNo'].upper()
            if len(train_no) >= 2 and train_no[0].isalpha() and train_no[1:].isdigit():
                validated['trainNo'] = train_no
            else:
                validated['trainNo'] = None
        
        # 验证日期格式
        date_fields = ['departDate', 'arriveDate']
        for field in date_fields:
            if field in validated and validated[field]:
                try:
                    from datetime import datetime
                    date_obj = datetime.strptime(validated[field], "%Y-%m-%d")
                    validated[field] = date_obj.strftime("%Y-%m-%d")
                except:
                    validated[field] = None
        
        return validated
    
    def get_train_type_from_number(self, train_no: str) -> str:
        """根据列车号获取列车类型"""
        if not train_no:
            return "未知"
        
        train_no = train_no.upper()
        if train_no.startswith('G'):
            return "高铁"
        elif train_no.startswith('D'):
            return "动车"
        elif train_no.startswith('K'):
            return "快速"
        elif train_no.startswith('T'):
            return "特快"
        elif train_no.startswith('Z'):
            return "直达"
        else:
            return "普通" 