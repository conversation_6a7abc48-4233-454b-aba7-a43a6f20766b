#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IntentFix 安装脚本
"""

from setuptools import setup, find_packages
import os

# 读取 README 文件
def read_readme():
    with open("README.md", "r", encoding="utf-8") as f:
        return f.read()

# 读取 requirements.txt
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="intentfix",
    version="1.0.0",
    author="IntentFix Team",
    author_email="<EMAIL>",
    description="智能意图修正模块，用于多轮对话中的意图校正和确认",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/intentfix/intentfix",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
        "rasa": [
            "rasa>=3.6.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "intentfix=run:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yml", "*.yaml", "*.json"],
    },
    keywords="nlp, intent, classification, dialogue, chatbot",
    project_urls={
        "Bug Reports": "https://github.com/intentfix/intentfix/issues",
        "Source": "https://github.com/intentfix/intentfix",
        "Documentation": "https://intentfix.readthedocs.io/",
    },
) 