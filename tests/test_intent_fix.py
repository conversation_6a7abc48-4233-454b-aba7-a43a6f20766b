#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
意图修正模块测试
包含单元测试和集成测试
"""

import unittest
import json
from datetime import datetime
from unittest.mock import Mock, patch

from core.models import IntentFixInput, IntentFixOutput, GptContent, SlotInfo, UserMessage
from core.intent_fix import IntentFixEngine
from core.rule_engine import RuleEngine
from core.parameter_validator import ParameterValidator
from domains.flight_domain import FlightDomainEngine
from domains.train_domain import TrainDomainEngine
from main import IntentFixService


class TestRuleEngine(unittest.TestCase):
    """规则引擎测试"""
    
    def setUp(self):
        self.rule_engine = RuleEngine()
    
    def test_keyword_matching(self):
        """测试关键词匹配"""
        # 测试延误登机关键词
        result = self.rule_engine.keyword_matching("CA1234航班延误了")
        self.assertEqual(result.intent, "延误登机")
        self.assertGreater(result.confidence, 0)
        
        # 测试预订航班关键词
        result = self.rule_engine.keyword_matching("帮我预订一张机票")
        self.assertEqual(result.intent, "预订航班")
        self.assertGreater(result.confidence, 0)
        
        # 测试改签关键词
        result = self.rule_engine.keyword_matching("我要改签")
        self.assertEqual(result.intent, "通用agent")
        self.assertGreater(result.confidence, 0)
    
    def test_pattern_matching(self):
        """测试模式匹配"""
        # 测试航班号模式
        result = self.rule_engine.pattern_matching("CA1234延误了")
        self.assertEqual(result.intent, "延误登机")
        self.assertEqual(result.confidence, 0.8)
        
        # 测试查询模式
        result = self.rule_engine.pattern_matching("查询航班信息")
        self.assertEqual(result.intent, "查询航班")
        self.assertEqual(result.confidence, 0.8)
    
    def test_context_correction(self):
        """测试上下文修正"""
        history = [{"intent": "延误登机"}]
        result = self.rule_engine.context_correction("CA1234", history)
        self.assertEqual(result.intent, "延误登机")
        self.assertEqual(result.confidence, 0.9)


class TestParameterValidator(unittest.TestCase):
    """参数校验器测试"""
    
    def setUp(self):
        self.validator = ParameterValidator()
    
    def test_validate_date_format(self):
        """测试日期格式校验"""
        # 测试标准格式
        result = self.validator.validate_date_format("2025-01-15")
        self.assertEqual(result, "2025-01-15")
        
        # 测试相对日期
        result = self.validator.validate_date_format("明天")
        self.assertIsNotNone(result)
        
        # 测试无效格式
        result = self.validator.validate_date_format("invalid-date")
        self.assertIsNone(result)
    
    def test_validate_flight_number(self):
        """测试航班号校验"""
        # 测试有效航班号
        result = self.validator.validate_flight_number("CA1234")
        self.assertEqual(result, "CA1234")
        
        # 测试无效航班号
        result = self.validator.validate_flight_number("1234")
        self.assertIsNone(result)
    
    def test_validate_city_name(self):
        """测试城市名称校验"""
        # 测试有效城市
        result = self.validator.validate_city_name("北京")
        self.assertEqual(result, "北京")
        
        # 测试无效城市
        result = self.validator.validate_city_name("不存在的城市")
        self.assertIsNone(result)
    
    def test_check_parameter_conflicts(self):
        """测试参数冲突检查"""
        current_slot = SlotInfo(
            departCity="上海",
            arriveCity="北京",
            departDate="2025-01-15"
        )
        
        history_slots = [
            SlotInfo(
                departCity="北京",
                arriveCity="上海",
                departDate="2025-01-15"
            )
        ]
        
        conflicts = self.validator.check_parameter_conflicts(current_slot, history_slots)
        self.assertEqual(len(conflicts), 2)  # departCity和arriveCity冲突


class TestIntentFixEngine(unittest.TestCase):
    """意图修正引擎测试"""
    
    def setUp(self):
        self.engine = IntentFixEngine()
    
    def test_extract_current_query(self):
        """测试提取当前查询"""
        user_history = [
            UserMessage(
                role="user",
                time=datetime.now(),
                type="text",
                content="CA1234航班什么时候起飞？"
            ),
            UserMessage(
                role="system",
                time=datetime.now(),
                type="text",
                content="正在查询..."
            )
        ]
        
        query = self.engine._extract_current_query(user_history)
        self.assertEqual(query, "CA1234航班什么时候起飞？")
    
    def test_compare_results(self):
        """测试结果比对"""
        from core.models import RuleResult
        
        rule_result = RuleResult(intent="延误登机", confidence=0.8)
        gpt_intent = "查询航班"
        previous_intent = "延误登机"
        
        candidates = self.engine._compare_results(rule_result, gpt_intent, previous_intent)
        self.assertEqual(len(candidates), 3)  # rule, model, history
        
        # 检查置信度排序
        confidences = [c.confidence for c in candidates]
        self.assertEqual(confidences, sorted(confidences, reverse=True))
    
    def test_determine_final_intent(self):
        """测试确定最终意图"""
        from core.models import IntentCandidate
        
        # 测试高置信度情况
        candidates = [
            IntentCandidate(intent="延误登机", confidence=0.9, source="rule"),
            IntentCandidate(intent="查询航班", confidence=0.3, source="model")
        ]
        
        final_intent, clarification = self.engine._determine_final_intent(candidates, "test")
        self.assertEqual(final_intent, "延误登机")
        self.assertIsNone(clarification)
        
        # 测试需要澄清的情况
        candidates = [
            IntentCandidate(intent="延误登机", confidence=0.6, source="rule"),
            IntentCandidate(intent="查询航班", confidence=0.5, source="model")
        ]
        
        final_intent, clarification = self.engine._determine_final_intent(candidates, "test")
        self.assertEqual(final_intent, "通用agent")
        self.assertIsNotNone(clarification)


class TestFlightDomainEngine(unittest.TestCase):
    """航班域引擎测试"""
    
    def setUp(self):
        self.engine = FlightDomainEngine()
    
    def test_flight_specific_logic(self):
        """测试航班域特定逻辑"""
        from core.models import IntentFixOutput, GptContent
        
        # 测试航班号格式化
        gptcontent = GptContent(
            intent="延误登机",
            flightNo="ca1234"
        )
        
        result = IntentFixOutput(
            gptcontent=gptcontent,
            resultReply=None
        )
        
        input_data = Mock()
        updated_result = self.engine._apply_flight_specific_logic(result, input_data)
        self.assertEqual(updated_result.gptcontent.flightNo, "CA1234")
    
    def test_missing_flight_number(self):
        """测试缺少航班号的情况"""
        from core.models import IntentFixOutput, GptContent
        
        gptcontent = GptContent(intent="延误登机")
        result = IntentFixOutput(gptcontent=gptcontent, resultReply=None)
        
        input_data = Mock()
        updated_result = self.engine._apply_flight_specific_logic(result, input_data)
        self.assertIsNotNone(updated_result.resultReply)


class TestTrainDomainEngine(unittest.TestCase):
    """列车域引擎测试"""
    
    def setUp(self):
        self.engine = TrainDomainEngine()
    
    def test_train_specific_logic(self):
        """测试列车域特定逻辑"""
        from core.models import IntentFixOutput, GptContent
        
        # 测试列车号格式化
        gptcontent = GptContent(
            intent="查询列车",
            trainNo="g1234"
        )
        
        result = IntentFixOutput(
            gptcontent=gptcontent,
            resultReply=None
        )
        
        input_data = Mock()
        updated_result = self.engine._apply_train_specific_logic(result, input_data)
        self.assertEqual(updated_result.gptcontent.trainNo, "G1234")
    
    def test_get_train_type(self):
        """测试获取列车类型"""
        self.assertEqual(self.engine.get_train_type_from_number("G1234"), "高铁")
        self.assertEqual(self.engine.get_train_type_from_number("D1234"), "动车")
        self.assertEqual(self.engine.get_train_type_from_number("K1234"), "快速")


class TestIntentFixService(unittest.TestCase):
    """意图修正服务测试"""
    
    def setUp(self):
        self.service = IntentFixService()
    
    def test_fix_intent_flight(self):
        """测试航班域意图修正"""
        gptcontent = {
            "intent": "查询航班",
            "flightNo": "CA1234"
        }
        
        slot = {
            "intent": "查询航班",
            "flightNo": "CA1234",
            "departCity": "北京",
            "arriveCity": "上海"
        }
        
        userHistory = [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "CA1234航班什么时候起飞？"
            }
        ]
        
        result = self.service.fix_intent(
            gptcontent=gptcontent,
            slot=slot,
            userHistory=userHistory,
            domain="flight"
        )
        
        self.assertIn("gptcontent", result)
        self.assertIn("intent", result["gptcontent"])
        self.assertEqual(result["gptcontent"]["flightNo"], "CA1234")
    
    def test_fix_intent_train(self):
        """测试列车域意图修正"""
        gptcontent = {
            "intent": "查询列车",
            "trainNo": "G1234"
        }
        
        slot = {
            "intent": "查询列车",
            "trainNo": "G1234",
            "departCity": "北京",
            "arriveCity": "上海"
        }
        
        userHistory = [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "G1234列车什么时候发车？"
            }
        ]
        
        result = self.service.fix_intent(
            gptcontent=gptcontent,
            slot=slot,
            userHistory=userHistory,
            domain="train"
        )
        
        self.assertIn("gptcontent", result)
        self.assertIn("intent", result["gptcontent"])
        self.assertEqual(result["gptcontent"]["trainNo"], "G1234")
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效输入
        result = self.service.fix_intent(
            gptcontent={},
            slot={},
            userHistory=[],
            domain="invalid_domain"
        )
        
        self.assertIn("gptcontent", result)
        self.assertEqual(result["gptcontent"]["intent"], "通用agent")
    
    def test_supported_domains(self):
        """测试支持的域列表"""
        domains = self.service.get_supported_domains()
        self.assertIn("flight", domains)
        self.assertIn("train", domains)
        self.assertIn("default", domains)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        self.service = IntentFixService()
    
    def test_complete_workflow(self):
        """测试完整工作流程"""
        # 模拟多轮对话
        test_cases = [
            {
                "name": "航班延误查询",
                "input": {
                    "gptcontent": {"intent": "查询航班", "flightNo": "CA1234"},
                    "slot": {"intent": "查询航班", "flightNo": "CA1234"},
                    "userHistory": [{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "CA1234延误了吗？"}],
                    "domain": "flight"
                },
                "expected_intent": "延误登机"
            },
            {
                "name": "列车预订",
                "input": {
                    "gptcontent": {"intent": "预订列车"},
                    "slot": {"intent": "预订列车", "departCity": "北京", "arriveCity": "上海"},
                    "userHistory": [{"role": "user", "time": "2025-01-15T10:30:00", "type": "text", "content": "帮我订一张从北京到上海的票"}],
                    "domain": "train"
                },
                "expected_intent": "预订列车"
            }
        ]
        
        for test_case in test_cases:
            with self.subTest(test_case["name"]):
                result = self.service.fix_intent(**test_case["input"])
                self.assertEqual(result["gptcontent"]["intent"], test_case["expected_intent"])


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2) 