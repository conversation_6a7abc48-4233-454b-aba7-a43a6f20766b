import jsonpath
import pandas as pd
import requests
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm  # 用于显示进度条

# 请求参数
url = "http://10.0.51.213:13313/?cmd=aichat"
domain = "tmall"


# 结果列表
results = []

# 并发数量 (每个session_id作为一个独立对话)
concurrent_requests = 15
# 读取测试问题分组数据
data = pd.read_csv("test.csv")
data=data[["session_id","question"]].dropna()
grouped = data.groupby('session_id')[['question']].agg(list).reset_index()

# 请求处理函数（处理单个问题）
def send_request(session_id, question):
    data = {
        "sessionId": session_id,
        "domain": domain,
        "msg": question.strip(),
        "onlyResult": "true",
        "stream": "false",
        "test":"test"
    }

    start = time.time()
    try:
        response = requests.post(url, data=data).json()
    except Exception as e:
        return {
            "session_id": session_id,
            "question": question.strip(),
            "cost": None,
            "bert_cost": None,
            "query_cost": None,
            "docSearch_cost": None,
            "gptSummary_cost": None,
            "docSearchQuery": None,
            "debugDocDatas": None,
            "createGptQuery": None,
            "modelResult":None,
            "content": None,
            "error": str(e)
        }
    end = time.time()

    cost = end - start
    bert_cost = jsonpath.jsonpath(response, "$..bert_cost")
    query_cost = jsonpath.jsonpath(response, "$..query_cost")
    docSearch_cost = jsonpath.jsonpath(response, "$..docSearch_cost")
    gptSummary_cost = jsonpath.jsonpath(response, "$..gptSummary_cost")

    # 提取新增字段
    docSearchQuery = jsonpath.jsonpath(response, "$..chainContext.docSearchQuery")
    debugDocDatas = jsonpath.jsonpath(response, "$..chainContext.debugDocDatas")
    createGptQuery = jsonpath.jsonpath(response, "$..paraMap.createGptQuery")
    modelResult = jsonpath.jsonpath(response, "$..modelResult")

    # 提取内容
    content = jsonpath.jsonpath(response, "$..content")
    try:
        content = [json.loads(content[0])["msg"]]
    except:
        pass

    return {
        "session_id": session_id,
        "question": question.strip(),
        "cost": cost,
        "bert_cost": bert_cost[0] if bert_cost else None,
        "query_cost": query_cost[0] if query_cost else None,
        "docSearch_cost": docSearch_cost[0] if docSearch_cost else None,
        "gptSummary_cost": gptSummary_cost[0] if gptSummary_cost else None,
        "docSearchQuery": docSearchQuery[0] if docSearchQuery else None,
        "debugDocDatas": ", ".join(debugDocDatas[0]) if debugDocDatas and debugDocDatas[0] else None,
        "createGptQuery": createGptQuery[0] if createGptQuery else None,
        "modelResult": modelResult[0] if modelResult else None,
        "content": content[0] if content else None,
        "error": None
    }

# 处理整个对话会话
def process_session(session_id,questions):
    session_results = []
    print(f"Processing session: {session_id}{questions}")
    for question in questions:
        result = send_request(session_id,question)
        session_results.append(result)
    return session_results

# 使用线程池并发执行多个会话
def run_concurrent_tests():
    start_time = time.time()
    success_count = 0
    total_cost = 0
    total_bert_cost = 0
    total_query_cost = 0
    total_docSearch_cost = 0
    bert_count = 0
    query_count = 0
    docSearch_count = 0
    total_gptSummary_cost = 0
    gptSummary_count = 0

    # 计算总问题数用于进度条
    total_questions =len(data)

    with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
        # 创建任务：每个session_id一个任务
        futures = {}
        for _, row in grouped.iterrows():
            session_id = row['session_id']+10000
            questions = row['question']
            future = executor.submit(process_session, session_id,questions)
            futures[future] = session_id

        # 添加 tqdm 进度条
        with tqdm(total=total_questions, desc="Processing", unit="question") as pbar:
            for future in as_completed(futures):
                session_results = future.result()
                results.extend(session_results)

                # 更新统计信息
                for result in session_results:
                    if result["content"] is not None and "请稍后再试" not in result["content"]:
                        success_count += 1
                        if result["cost"] is not None:
                            total_cost += result["cost"]
                    if result["bert_cost"] is not None:
                        total_bert_cost += result["bert_cost"]
                        bert_count += 1
                    if result["query_cost"] is not None:
                        total_query_cost += result["query_cost"]
                        query_count += 1
                    if result["docSearch_cost"] is not None:
                        total_docSearch_cost += result["docSearch_cost"]
                        docSearch_count += 1
                    if result["gptSummary_cost"] is not None:
                        total_gptSummary_cost += result["gptSummary_cost"]
                        gptSummary_count += 1

                    # 更新进度条（每个问题更新一次）
                    pbar.update(1)

    end_time = time.time()
    total_time = end_time - start_time
    qps = success_count / total_time if total_time > 0 else 0
    avg_cost = total_cost / success_count if success_count > 0 else 0
    avg_bert_cost = total_bert_cost / bert_count if bert_count > 0 else 0
    avg_query_cost = total_query_cost / query_count if query_count > 0 else 0
    avg_docSearch_cost = total_docSearch_cost / docSearch_count if docSearch_count > 0 else 0
    avg_gptSummary_cost = total_gptSummary_cost / gptSummary_count if gptSummary_count > 0 else 0

    print(f"总耗时: {total_time:.2f} 秒")
    print(f"QPS: {qps:.2f}")
    print(f"平均请求耗时: {avg_cost:.2f} 秒")
    print(f"请求成功数: {success_count}")
    print(f"平均 BERT 耗时: {avg_bert_cost:.2f} 秒")
    print(f"平均 Query 耗时: {avg_query_cost:.2f} 秒")
    print(f"平均 DocSearch 耗时: {avg_docSearch_cost:.2f} 秒")
    print(f"平均 GPT Summary 耗时: {avg_gptSummary_cost:.2f} 秒")

# 执行并发测试
run_concurrent_tests()

# 将结果保存到 Pandas DataFrame
df = pd.DataFrame(results)

# 保存为 CSV 文件
df.to_csv("多轮对话测试结果.csv", index=False, encoding="utf-8-sig")

# 打印结果
print("测试完成，结果已保存到 多轮对话测试结果.csv")