#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试tmall domain功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.models import GptContent, IntentFixInput, IntentFixOutput, SlotInfo, UserMessage
from core.intent_fix import IntentFixEngine
from datetime import datetime


def test_tmall_domain():
    """测试tmall domain功能"""
    
    # 初始化意图修正引擎
    engine = IntentFixEngine()
    
    # 测试用例：使用tmall domain的实际数据格式
    test_case = {
        "gptcontent": {
            "intent": "flights",
            "返回日期": "",
            "航班车次号": "CA1234",
            "dst": "北京",
            "org": "",
            "questions": [],
            "出发日期": "2025-07-30",
            "itinerary_num": "",
            "主业务类型": "机票",
            "airport": ""
        },
        "slot": {
            "intent": "flights",
            "出发日期": "2025-07-30",
            "返回日期": "",
            "主业务类型": "机票",
            "航班车次号": "CA1234",
            "org": "",
            "dst": "北京"
        },
        "userHistory": [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "CA1234航班什么时候起飞？"
            },
            {
                "role": "system",
                "time": "2025-01-15T10:30:05",
                "type": "text",
                "content": "正在查询CA1234航班信息..."
            }
        ],
        "domain": "tmall"
    }
    
    print("=" * 60)
    print("tmall domain测试")
    print("=" * 60)
    
    print(f"\n输入数据:")
    print(f"GPT内容: {test_case['gptcontent']}")
    print(f"Slot信息: {test_case['slot']}")
    print(f"Domain: {test_case['domain']}")
    
    # 构建输入数据
    input_data = IntentFixInput(
        gptcontent=GptContent(**test_case["gptcontent"]),
        slot=SlotInfo(**test_case["slot"]),
        userHistory=[UserMessage(**msg) for msg in test_case["userHistory"]],
        domain=test_case["domain"]
    )
    
    try:
        # 执行意图修正
        result = engine.fix_intent(input_data)
        
        print(f"\n输出结果:")
        print(f"意图: {result.gptcontent.intent}")
        print(f"返回日期: {result.gptcontent.返回日期}")
        print(f"航班车次号: {result.gptcontent.航班车次号}")
        print(f"目的地: {result.gptcontent.dst}")
        print(f"出发地: {result.gptcontent.org}")
        print(f"问题列表: {result.gptcontent.questions}")
        print(f"出发日期: {result.gptcontent.出发日期}")
        print(f"行程编号: {result.gptcontent.itinerary_num}")
        print(f"主业务类型: {result.gptcontent.主业务类型}")
        print(f"机场: {result.gptcontent.airport}")
        
        if result.resultReply:
            print(f"\n澄清消息: {result.resultReply}")
        
        # 验证输出格式是否符合tmall domain要求
        print(f"\n验证输出格式:")
        output_dict = result.gptcontent.dict()
        expected_fields = ["返回日期", "航班车次号", "dst", "org", "questions", "出发日期", "itinerary_num", "主业务类型", "airport"]
        
        for field in expected_fields:
            if field in output_dict:
                print(f"✓ {field}: {output_dict[field]}")
            else:
                print(f"✗ {field}: 缺失")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_tmall_with_query_extraction():
    """测试tmall domain的查询提取功能"""
    
    engine = IntentFixEngine()
    
    # 测试用例：包含查询提取
    test_case = {
        "gptcontent": {
            "intent": "flights",
            "返回日期": "",
            "航班车次号": "",
            "dst": "",
            "org": "",
            "questions": [],
            "出发日期": "",
            "itinerary_num": "",
            "主业务类型": "",
            "airport": ""
        },
        "slot": {
            "intent": "flights",
            "出发日期": "",
            "返回日期": "",
            "主业务类型": "",
            "航班车次号": "",
            "org": "",
            "dst": ""
        },
        "userHistory": [
            {
                "role": "user",
                "time": "2025-01-15T10:30:00",
                "type": "text",
                "content": "明天从上海到北京的MU5678航班"
            }
        ],
        "domain": "tmall"
    }
    
    print("\n" + "=" * 60)
    print("tmall domain查询提取测试")
    print("=" * 60)
    
    input_data = IntentFixInput(
        gptcontent=GptContent(**test_case["gptcontent"]),
        slot=SlotInfo(**test_case["slot"]),
        userHistory=[UserMessage(**msg) for msg in test_case["userHistory"]],
        domain=test_case["domain"]
    )
    
    try:
        result = engine.fix_intent(input_data)
        
        print(f"\n查询: {test_case['userHistory'][0]['content']}")
        print(f"提取结果:")
        print(f"  出发地: {result.gptcontent.org}")
        print(f"  目的地: {result.gptcontent.dst}")
        print(f"  出发日期: {result.gptcontent.出发日期}")
        print(f"  航班车次号: {result.gptcontent.航班车次号}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_tmall_domain()
    test_tmall_with_query_extraction()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60) 