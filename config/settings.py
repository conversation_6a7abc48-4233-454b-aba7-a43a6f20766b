#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IntentFix 配置文件
管理项目的各种设置和参数
"""

import os
from typing import Dict, List, Any


class Settings:
    """项目设置类"""
    
    # 基础配置
    PROJECT_NAME = "IntentFix"
    VERSION = "1.0.0"
    DEBUG = os.getenv("DEBUG", "False").lower() == "true"
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE = os.getenv("LOG_FILE", "logs/intentfix.log")
    
    # 意图修正配置
    CONFIDENCE_THRESHOLD = float(os.getenv("CONFIDENCE_THRESHOLD", "0.7"))
    CLARIFICATION_THRESHOLD = float(os.getenv("CLARIFICATION_THRESHOLD", "0.5"))
    MAX_CLARIFICATION_ATTEMPTS = int(os.getenv("MAX_CLARIFICATION_ATTEMPTS", "3"))
    
    # 规则引擎配置
    RULE_ENGINE = {
        "keyword_weight": 0.6,
        "pattern_weight": 0.8,
        "context_weight": 0.9,
        "enable_fuzzy_match": True,
        "fuzzy_threshold": 0.8
    }
    
    # Rasa配置
    RASA = {
        "model_path": "models/",
        "training_data_path": "data/",
        "enable_training": True,
        "confidence_threshold": 0.6,
        "max_candidates": 5
    }
    
    # 参数校验配置
    PARAMETER_VALIDATION = {
        "enable_strict_validation": True,
        "auto_correct": True,
        "date_formats": [
            "%Y-%m-%d",
            "%Y/%m/%d",
            "%m-%d",
            "%m/%d"
        ],
        "flight_number_pattern": r"[A-Z]{2}\d{3,4}",
        "train_number_pattern": r"[A-Z]\d{1,4}",
        "city_mapping": {
            "北京": "北京",
            "上海": "上海",
            "广州": "广州",
            "深圳": "深圳",
            "杭州": "杭州",
            "南京": "南京",
            "成都": "成都",
            "武汉": "武汉",
            "西安": "西安",
            "重庆": "重庆",
            "天津": "天津",
            "青岛": "青岛",
            "大连": "大连",
            "厦门": "厦门",
            "苏州": "苏州"
        }
    }
    
    # 业务域配置
    DOMAINS = {
        "flight": {
            "name": "航班域",
            "intents": [
                "查询航班", "预订航班", "改签", "退票", "通用agent"
            ],
            "required_slots": {
                "预订航班": ["departCity", "arriveCity", "departDate"]
            },
            "optional_slots": [
                "departDate", "arriveDate", "departCity", "arriveCity", 
                "flightNo", "mainBusinessType"
            ]
        },
        "train": {
            "name": "列车域",
            "intents": [
                "查询列车", "预订列车", "改签", "退票", "通用agent"
            ],
            "required_slots": {
                "预订列车": ["departCity", "arriveCity", "departDate"]
            },
            "optional_slots": [
                "departDate", "arriveDate", "departCity", "arriveCity", 
                "trainNo", "mainBusinessType"
            ]
        }
    }
    
    # 关键词规则配置
    KEYWORD_RULES = {
        "通用agent": [
            "改签", "重新预定", "重新预订", "重新预约", "重新订票",
            "取消", "退票", "退款", "修改", "变更", "调整",
            "订单", "预订", "预约", "订票", "买票"
        ],
        # 已移除 “延误登机”
        "查询航班": [
            "查询", "查找", "搜索", "找", "查", "航班", "飞机",
            "什么时候", "几点", "时间", "时刻表"
        ],
        "预订航班": [
            "预订", "预约", "订票", "买票", "购买", "订", "预约",
            "要订", "想订", "帮我订", "帮我预约"
        ],
        "查询天气": [
            "天气", "气温", "温度", "下雨", "晴天", "阴天",
            "天气预报", "今天天气", "明天天气"
        ]
    }
    
    # 模式规则配置
    PATTERN_RULES = {
        "通用agent": [
            r".*取消.*订单.*",
            r".*退票.*",
            r".*改签.*",
            r".*重新.*预定.*",
            r".*修改.*订单.*"
        ],
        # 已移除 “延误登机”
        "查询航班": [
            r".*查询.*航班.*",
            r".*什么时候.*",
            r".*几点.*",
            r".*时间.*"
        ],
        "预订航班": [
            r".*预订.*航班.*",
            r".*订票.*",
            r".*买票.*",
            r".*预约.*"
        ]
    }
    
    # 缓存配置
    CACHE = {
        "enable": True,
        "ttl": 3600,  # 缓存时间（秒）
        "max_size": 1000,  # 最大缓存条目数
        "backend": "memory"  # 缓存后端：memory/redis
    }
    
    # 性能配置
    PERFORMANCE = {
        "max_concurrent_requests": 100,
        "request_timeout": 30,  # 请求超时时间（秒）
        "enable_profiling": False,
        "profiling_output": "profiling_results.json"
    }
    
    # 错误处理配置
    ERROR_HANDLING = {
        "enable_retry": True,
        "max_retries": 3,
        "retry_delay": 1,  # 重试延迟（秒）
        "log_errors": True,
        "return_error_details": False  # 是否返回详细错误信息
    }
    
    @classmethod
    def get_domain_config(cls, domain: str) -> Dict[str, Any]:
        """获取指定域的配置"""
        return cls.DOMAINS.get(domain, {})
    
    @classmethod
    def get_keyword_rules(cls, domain: str = None) -> Dict[str, List[str]]:
        """获取关键词规则"""
        if domain and domain in cls.DOMAINS:
            # 可以在这里添加域特定的关键词规则
            pass
        return cls.KEYWORD_RULES
    
    @classmethod
    def get_pattern_rules(cls, domain: str = None) -> Dict[str, List[str]]:
        """获取模式规则"""
        if domain and domain in cls.DOMAINS:
            # 可以在这里添加域特定的模式规则
            pass
        return cls.PATTERN_RULES
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置的有效性"""
        try:
            # 验证置信度阈值
            assert 0 <= cls.CONFIDENCE_THRESHOLD <= 1
            assert 0 <= cls.CLARIFICATION_THRESHOLD <= 1
            
            # 验证域配置
            for domain, config in cls.DOMAINS.items():
                assert "intents" in config
                assert "required_slots" in config
                assert "optional_slots" in config
            
            # 验证参数校验配置
            assert "date_formats" in cls.PARAMETER_VALIDATION
            assert "city_mapping" in cls.PARAMETER_VALIDATION
            
            return True
        except AssertionError:
            return False
    
    @classmethod
    def get_logging_config(cls) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "standard": {
                    "format": cls.LOG_FORMAT
                },
            },
            "handlers": {
                "default": {
                    "level": cls.LOG_LEVEL,
                    "formatter": "standard",
                    "class": "logging.StreamHandler",
                },
                "file": {
                    "level": cls.LOG_LEVEL,
                    "formatter": "standard",
                    "class": "logging.FileHandler",
                    "filename": cls.LOG_FILE,
                    "mode": "a",
                }
            },
            "loggers": {
                "": {
                    "handlers": ["default", "file"],
                    "level": cls.LOG_LEVEL,
                    "propagate": False
                }
            }
        }


# 创建全局设置实例
settings = Settings()


def load_config_from_file(config_file: str) -> bool:
    """从文件加载配置"""
    try:
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 更新设置
        for key, value in config_data.items():
            if hasattr(settings, key):
                setattr(settings, key, value)
        
        return True
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return False


def save_config_to_file(config_file: str) -> bool:
    """保存配置到文件"""
    try:
        import json
        config_data = {}
        
        # 收集所有配置
        for key in dir(settings):
            if not key.startswith('_') and not callable(getattr(settings, key)):
                config_data[key] = getattr(settings, key)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False


if __name__ == "__main__":
    # 验证配置
    if settings.validate_config():
        print("配置验证通过")
    else:
        print("配置验证失败")
    
    # 打印当前配置
    print(f"项目名称: {settings.PROJECT_NAME}")
    print(f"版本: {settings.VERSION}")
    print(f"调试模式: {settings.DEBUG}")
    print(f"置信度阈值: {settings.CONFIDENCE_THRESHOLD}")
    print(f"支持的域: {list(settings.DOMAINS.keys())}") 