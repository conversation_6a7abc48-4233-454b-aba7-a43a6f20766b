#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IntentFix Web API 服务
提供REST API接口进行意图修正
"""

import sys
import os
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from main import IntentFixService
from config.settings import settings

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT,
    handlers=[
        logging.FileHandler(settings.LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="IntentFix API",
    description="智能意图修正服务API",
    version=settings.VERSION,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建服务实例
service = IntentFixService()


# 请求模型
class IntentFixRequest(BaseModel):
    """意图修正请求模型"""
    gptcontent: Dict[str, Any] = Field(..., description="大模型输出的初步意图和槽位参数")
    slot: Dict[str, Any] = Field(..., description="Tripnow提取的槽位和意图信息")
    userHistory: List[Dict[str, Any]] = Field(..., description="用户与系统的历史对话")
    domain: str = Field("flight", description="业务域")


# 响应模型
class IntentFixResponse(BaseModel):
    """意图修正响应模型"""
    success: bool = Field(..., description="是否成功")
    data: Dict[str, Any] = Field(..., description="修正结果")
    message: str = Field("", description="响应消息")
    timestamp: str = Field(..., description="时间戳")


# 健康检查模型
class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="版本号")
    timestamp: str = Field(..., description="时间戳")
    uptime: str = Field(..., description="运行时间")


# 全局变量
start_time = datetime.now()


@app.get("/", response_model=Dict[str, str])
async def root():
    """根路径"""
    return {
        "message": "IntentFix API 服务正在运行",
        "version": settings.VERSION,
        "docs": "/docs"
    }


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    uptime = datetime.now() - start_time
    return HealthResponse(
        status="healthy",
        version=settings.VERSION,
        timestamp=datetime.now().isoformat(),
        uptime=str(uptime)
    )


@app.post("/api/v1/fix-intent", response_model=IntentFixResponse)
async def fix_intent(request: IntentFixRequest):
    """
    意图修正API
    
    - **gptcontent**: 大模型输出的初步意图和槽位参数
    - **slot**: Tripnow提取的槽位和意图信息
    - **userHistory**: 用户与系统的历史对话
    - **domain**: 业务域（默认：flight）
    """
    try:
        logger.info(f"收到意图修正请求 - 域: {request.domain}")
        logger.info("请求入参: %s", {
            "gptcontent": request.gptcontent,
            "slot": request.slot,
            "userHistory": request.userHistory,
            "domain": request.domain,
        })
        
        # 执行意图修正
        result = service.fix_intent(
            gptcontent=request.gptcontent,
            slot=request.slot,
            userHistory=request.userHistory,
            domain=request.domain
        )
        
        logger.info(f"意图修正完成 - 域: {request.domain}, 最终意图: {result.get('gptcontent', {}).get('intent', 'unknown')}")
        logger.info("响应出参: %s", result)
        
        return IntentFixResponse(
            success=True,
            data=result,
            message="意图修正成功",
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"意图修正失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"意图修正失败: {str(e)}"
        )


@app.get("/api/v1/domains", response_model=Dict[str, Any])
async def get_supported_domains():
    """获取支持的域列表"""
    try:
        domains = service.get_supported_domains()
        return {
            "success": True,
            "data": {
                "domains": domains,
                "count": len(domains)
            },
            "message": "获取支持的域列表成功",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取域列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取域列表失败: {str(e)}"
        )


@app.get("/api/v1/config", response_model=Dict[str, Any])
async def get_config():
    """获取服务配置"""
    try:
        return {
            "success": True,
            "data": {
                "project_name": settings.PROJECT_NAME,
                "version": settings.VERSION,
                "debug": settings.DEBUG,
                "confidence_threshold": settings.CONFIDENCE_THRESHOLD,
                "clarification_threshold": settings.CLARIFICATION_THRESHOLD,
                "supported_domains": list(settings.DOMAINS.keys()),
                "log_level": settings.LOG_LEVEL
            },
            "message": "获取配置成功",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取配置失败: {str(e)}"
        )


@app.post("/api/v1/batch-fix", response_model=Dict[str, Any])
async def batch_fix_intent(requests: List[IntentFixRequest]):
    """
    批量意图修正API
    
    - **requests**: 意图修正请求列表
    """
    try:
        logger.info(f"收到批量意图修正请求 - 数量: {len(requests)}")
        try:
            preview = [{
                "gptcontent": r.gptcontent,
                "slot": r.slot,
                "userHistory": r.userHistory,
                "domain": r.domain
            } for r in requests[:5]]
            logger.info("批量请求入参预览(最多5条): %s", preview)
        except Exception:
            pass
        
        results = []
        for i, request in enumerate(requests):
            try:
                result = service.fix_intent(
                    gptcontent=request.gptcontent,
                    slot=request.slot,
                    userHistory=request.userHistory,
                    domain=request.domain
                )
                
                item = {
                    "index": i,
                    "success": True,
                    "data": result,
                    "domain": request.domain
                }
                results.append(item)
                logger.info("批量第 %s 个出参: %s", i, item)
                
            except Exception as e:
                logger.error(f"批量处理第 {i+1} 个请求失败: {str(e)}")
                results.append({
                    "index": i,
                    "success": False,
                    "error": str(e),
                    "domain": request.domain
                })
        
        return {
            "success": True,
            "data": {
                "results": results,
                "total": len(requests),
                "success_count": len([r for r in results if r["success"]]),
                "error_count": len([r for r in results if not r["success"]])
            },
            "message": f"批量处理完成 - 成功: {len([r for r in results if r['success']])}/{len(requests)}",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"批量意图修正失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量意图修正失败: {str(e)}"
        )


@app.post("/api/v1/add-domain")
async def add_custom_domain(domain: str, domain_config: Dict[str, Any]):
    """
    添加自定义域
    
    - **domain**: 域名
    - **domain_config**: 域配置
    """
    try:
        # 这里需要实现动态添加域的逻辑
        # 暂时返回成功响应
        logger.info(f"添加自定义域: {domain}")
        
        return {
            "success": True,
            "data": {
                "domain": domain,
                "message": "域添加成功"
            },
            "message": f"域 {domain} 添加成功",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"添加域失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"添加域失败: {str(e)}"
        )


# 启动事件
@app.on_event("startup")
async def startup_event():
    """服务启动事件"""
    logger.info("IntentFix API 服务启动")
    logger.info(f"版本: {settings.VERSION}")
    logger.info(f"支持的域: {service.get_supported_domains()}")
    logger.info(f"配置验证: {'通过' if settings.validate_config() else '失败'}")


# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """服务关闭事件"""
    logger.info("IntentFix API 服务关闭")


if __name__ == "__main__":
    import uvicorn
    
    # 启动服务
    uvicorn.run(
        "api.app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=settings.LOG_LEVEL.lower()
    ) 